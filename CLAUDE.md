# Global Preferences

* Please commit changes when finished.
* I use zsh shell.
* Fail Fast - raise exceptions and let them bubble up. Let the code break on error.
* Don't add fallbacks or backwards compatability unless explicitly asked to.
* Don't change tests to fit the code. Tests should always reflect expected functionality and behaviour. If tests fail because the code doesn't do what is expected, **fix the code** not the test.
* Always check the latest docs first, download from the web or use the Context7 tool.
* We don't do silent failures, all failures MUST appear in logs where appropriate or cause the application to fail if appropriate. If an error should not cause the application to fail then it should be logged as a warning, always log errors one way or another.

# Coding Philosophy
- Max 300-400 lines per file, please split your output into multiple files, creating packages as you need.
- Add explicit error logging and exception propagation.
- Do not put in runtime checks for things you are uncertain of at compile time (such as getattr("xyz")) instead FAIL FAST. Avoid silent degradation, fail fast.
- Fail fast, don't try to work out how to continue raise an Error
- If a list can only contain unique values consider using a set instead.
- Don't 'swallow' exceptions or failure states, propagate them.
- Where it makes sense use a state machine to ensure that only valid states and state transitions occur. A typical clue it's needed is that an object/class has an enumeration or a set of possible string values.
- Where data and function are co-related use OOP paradigms. But avoid inheritance more than 2 deep.
- Where data has multiple transformations applied to it, use functional paradigms. If the transformations are many, use 'stream' paradigms.
- **Where possible** aim for immutability and atomicity. A state change should ideally create a new object.
- Prefer the native paradigms of the programming language and frameworks you are using.
- Prefer typed classes over dictionaries, maps and other de-typed constructs.

# The project

## Project Structure
This is a mono-repo

```
/backoffice/     # Python backend
  /src/          # Python code
    /eko/        # Core modules
    /tests/on_commit        # Unit tests run on every commit
    /tests/integration      # Integration tests
/apps/           # Frontend apps
  /cms/          # CMS (Payload) / Website
  /customer/     # Customer App
/packages/       # Shared libraries
```

# Frontend (React/Typescript)

- Run `tsc --noEmit`and all playwright tests before committing.
- New shadcn components or any cross app reusable components can go in packages/ui

## Customer App /apps/customer

### Key UI Components
#### Design System
  - Glass-morphism with heavily rounded elements
  - Translucent, frosted glass-like surfaces with backdrop blur.
  - Translucency reduced when there is text to display, for clarity.
  - Generous border radii (standard: 1.5rem) for a modern, approachable feel.
  - Use of colour, not just monotone. Several glass colours classes provided, add more if needed.
  - Subtle shadows and animations for depth and interactivity
#### Cards
  - Glass-morphism cards with rounded corners
  - Consistent hover animations with subtle lift effects
#### Subtle Animations 
- Use animations to provide UX clues to the user, i.e. clickable, scrollable, more information etc.
- Use transitions to help the user follow changes in UI layout.

Read apps/customer/tests/CLAUDE.md if you are working on tests for the customer app.

### Payload CMS /apps/cms

The CMS uses Payload with configurable components:

- Create `config.ts` files to define schemas for CMS configuration
- Use array fields for lists and `link` type for navigation
- Include fallbacks for missing data
- Use Next.js `revalidateTag` for cache refresh

- **Blocks**: Content, Features, Banner in `/blocks/`
  - Block-based content system with standardized wrappers
  - Uses `BlockWrapper` component for consistent glass-morphism styling
  - `GlassCard` component for image-based content cards
  - `CardGeneric` component for flexible content cards
- **Design**: Consistent spacing, heavily rounded corners, hover states


```typescript
// Component config pattern
{
  name: 'sectionName',
  type: 'array',
  label: 'Label',
  fields: [...],
  admin: {
    initCollapsed: true,
    components: { RowLabel: '@/Path/RowLabel#RowLabel' }
  },
}
```


# Python Backend


### Common Commands & Style Guidelines
```zsh
# Python commands in backoffice
cd backoffice/src && uv run python
cd backoffice/src && uv run python src/cli.py claude-self-test  # Self-test
cd backoffice/src && uv run python-m pytest src/tests      # Run tests
cd backoffice/src && uvx ty check <filename>              # Typecheck

# Web apps
pnpm build                      # Build frontend
```


### Python Code Style
- I use uv
- Max 300-400 lines per file, please split your output into multiple files, creating packages as you need.
- Type annotations with `Optional[T]` for nullable values
- Defensive validation of function parameters, throw ValueErrors if incorrect.
- Do not put in runtime checks for things you are uncertain of at compile time (such as getattr("xyz")) instead FAIL FAST. Avoid silent degradation, fail fast.
- `snake_case` variables, `PascalCase` classes, `UPPER_SNAKE_CASE` constants
- Sort imports: stdlib → third-party → local
- Use loguru for logging (use **logger.exception** for exception recording not logger.error)
- Document with docstrings including param/return types, only describe the purpose of the code not how it works.
- Prefer non-Optional fields to Optional ones, never make a pydantic field List or Dict Optional, use default_factory=
- Fail fast, don't try to work out how to continue raise an Error
- When data and functionality are closely related use classes not functions. No need to be Object Oriented about everything, just when it makes sense.
- Prefer to use  Pydantic classes over dictionaries, only use non-typed objects such as dicts, dataframes etc. in performance sensitive code (like clustering etc.).  The core models of a process should be **Pydantic** classes.
- You have plenty of memory so all non-trivial data should be stored as a graph of fully realised objects, not referenced by a list of database ids. If a model is excessively memory hungry I will deal with that.
- If a list can only contain unique values consider using a set instead.
- Pass contextual data down through function calls, prefer Pydantic classes rather than large lists of parameters.
- Don't 'swallow' exceptions, log them and raise
- Use eko.settings to save any 'magic' numnbers like hyperparameters etc.
- When faced with circular imports it's usually best to break files down into smaller chunks.



### Key Packages

Ignore anything in _deprecated_code_, do not use or edit this code.

- `cli`: Command line interface
- `eko.db.data`: DAOs (see statement.py as example)
- `eko.llm`: LLM integration (providers, prompts)
- `eko.models`: Pydantic models (not ORM)
- `eko.models.vector`: Embedding models (DEMISE, TREVR)
- `eko.analysis_v2.effects`: Effect analysis system
- `eko.analysis_v2.responsibility`: Responsibility analysis system
- `eko.nlp`: NLP processing utilities
- `eko.domains`: Web domain management
- `eko.entities`: Named entity management

### Core Patterns

**DAOs**:

Each DAO is tied to a Pydantic model class.

Create methods return the model class with `id` set
Retrieve methods return the model class tied to the DAO
Find/select methods return a list of the model class tied to the DAO
Update methods take a model, return the same.
Delete methods return the id of the deleted model.

**Effect Analysis**:
- Identifies effects from entity statements
- Clusters statements and generates flags
- Effect flags are evaluated against the DEMISE model to calculate domain vectors
- Effect flags are deduplicated using DBSCAN clustering on the Domain part of the DEMISE model
- Claims and promises analysis system:
  - `claims_and_promises.py`: Shared code for both systems
  - `claim_evidence.py`: Analyzes claims vs historical evidence
  - `promise_analysis.py`: Analyzes promises vs future evidence
  ```bash
  # Run claims analysis
  cd backoffice/src && uv run cli.py analyze-claims --entity "EntityShortId" --start-year 2019 --end-year 2025

  # Run promises analysis
  cd backoffice/src && uv run cli.py analyze-promises --entity "EntityShortId" --start-year 2019 --end-year 2025
  ```

**DEMISE Model for Effect Flags**:
- Each effect flag has a DEMISE model calculated from its title, summary and analysis text
- The specialized `extract_effect_flag_demise()` function is used (not the same as for statements)
- Effect flags use a different prompt that acknowledges they are analyses, not raw statements
- The DEMISE model is stored in the `full_demise_centroid` field of the `EffectFlagModel`
- The Domain part of the DEMISE model is used for clustering similar flags
- DBSCAN clustering with minimum cluster size of 1 is used for deduplication
- Flags are only merged if they have similar years (within 2 years)

**PipelineTracker**: Located in `pipeline_tracker.py`
- Records metrics about pipeline stages in 'dash' schema
- Provides tracking methods for each stage


**CLI commands**:
- Use hyphenated names like `create-effect-flags-viz`
- Add to cli package in dedicated files (not cli.py)
- Implement with named parameters

## Database


There are two databases in this application the analytics database `./bin/run_in_db.sh` that is connected to with `get_bo_conn()` and the customer database `./bin/run_in_customer_db.sh` `get_cus_conn()`. The python application in `backofffice` does almost all it's work in the analytics database. The final results are placed in the xfer_ tables. These are then synced to the customer database in `/Users/<USER>/IdeaProjects/mono-repo/backoffice/src/eko/db/sync.py`

The customer application is in apps/customer and makes use of those copied xfer_ tables to power the web application from it's database.

### Analytics Database

All ana_ tables should include a run_id as analysis runs are seperate from each other. See RunData DAO.

**Schemas**: `public` (main), `dash` (metrics),

Don't store ids in array columns, always use a join table. Always add foreign key constraints. If an ana_ table maps onto a kg_ table then deletions and updates in the kg_ table should cascade to the ana_ table.

#### Write Acccess
use the run_in_db.sh script for read/write access to the database.

```
./bin/run_in_db.sh "SQL CODE"
```
or
```./bin/run_in_db.sh  < file.sql```

### Customer Database

#### Write Acccess
Use the run_in_.sh script for read/write access to the database.

```
./bin/run_in_customer_db.sh "SQL CODE"
```
or
```./bin/run_in_customer_db.sh  < file.sql```

- The xfer_ tables are the link between customer and analytics databases. They are intentionally without any real schema or constraints for flexibility.
- The acc_ and cus_ tables are normal tables and have constraints, foreign keys etc.
- The api_queue table is only for front_end to back_end communication.


### File Storage

- **Always use `eko_var_path`**:
  ```python
  from eko import eko_var_path
  os.path.join(eko_var_path, "cache/entity_cache")
  ```
- **Never use `../var/` paths**
- **Common directories**: `cache/`, `files/`, `embed/`, `models/`

### Best Practices
- Use loguru (logger.exception, not logger.error)
- Store temp files in `tmp` at root
- Git: Use `git mv` and `git rm` (not plain mv/rm)
- Run pyright before committing


# Issues

We use Linear for issues, the issues are in the form EKO-123. If you are working on an issue please include the issue in your commit messages. You can also add comments to the issue, but keep them short.


<important>
Your workflow is typically:

- Check Linear for details on the issue, including attachments
- Plan work
- Do work
- Write tests
- Confirm tests pass
- For web: `tsc --noEmit` then run playwright tests `npx playwright test --reporter=line`
  Read more on writing playwright tests here: apps/customer/tests/CLAUDE.md
- For python run the pytests in tests.on_commit and run `uvx ty check` on the changed files.
- Commit changes, with a verbose comment including the Linear issue ID
- Update Linear with a report on what you have done.
</important>
