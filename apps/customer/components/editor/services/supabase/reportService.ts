import { createClient } from '@/app/supabase/client'
import { ReportComponent } from '../../types'
import { logger } from '../utils/logger'

export class ReportService {
  private supabase = createClient()

  async saveReport(documentId: string, content: string, data: any): Promise<void> {
    logger.info('ReportService', 'saveReport', `Saving report ${documentId}`)
    
    try {
      const { error } = await this.supabase
        .from('documents')
        .update({
          content,
          data,
          updated_at: new Date().toISOString()
        })
        .eq('id', documentId)

      if (error) {
        logger.error('ReportService', 'saveReport', `Failed to save report ${documentId}`, error)
        throw new Error(`Failed to save report: ${error.message}`)
      }

      logger.info('ReportService', 'saveReport', `Successfully saved report ${documentId}`)
    } catch (error) {
      logger.error('ReportService', 'saveReport', `Error saving report ${documentId}`, error as Error)
      throw error
    }
  }

  async loadReport(documentId: string): Promise<{ content: string; data: any } | null> {
    logger.info('ReportService', 'loadReport', `Loading report ${documentId}`)
    
    try {
      const { data: document, error } = await this.supabase
        .from('documents')
        .select('content, data')
        .eq('id', documentId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          logger.warn('ReportService', 'loadReport', `Report ${documentId} not found`)
          return null
        }
        logger.error('ReportService', 'loadReport', `Failed to load report ${documentId}`, error)
        throw new Error(`Failed to load report: ${error.message}`)
      }

      logger.info('ReportService', 'loadReport', `Successfully loaded report ${documentId}`)
      return {
        content: document.content as string,
        data: document.data
      }
    } catch (error) {
      logger.error('ReportService', 'loadReport', `Error loading report ${documentId}`, error as Error)
      throw error
    }
  }

  async updateComponent(documentId: string, component: ReportComponent): Promise<void> {
    logger.debug('ReportService', 'updateComponent', `Updating component ${component.id} in report ${documentId}`)
    
    try {
      // This is a placeholder - the actual implementation would depend on your schema
      // You might store components in a separate table or as part of the document data
      const { error } = await this.supabase
        .from('report_components')
        .upsert({
          document_id: documentId,
          component_id: component.id,
          type: component.type,
          status: component.status,
          data: component,
          updated_at: new Date().toISOString()
        })

      if (error) {
        logger.error('ReportService', 'updateComponent', `Failed to update component ${component.id}`, error)
        throw new Error(`Failed to update component: ${error.message}`)
      }

      logger.debug('ReportService', 'updateComponent', `Successfully updated component ${component.id}`)
    } catch (error) {
      logger.error('ReportService', 'updateComponent', `Error updating component ${component.id}`, error as Error)
      throw error
    }
  }

  async deleteComponent(documentId: string, componentId: string): Promise<void> {
    logger.info('ReportService', 'deleteComponent', `Deleting component ${componentId} from report ${documentId}`)
    
    try {
      const { error } = await this.supabase
        .from('report_components')
        .delete()
        .eq('document_id', documentId)
        .eq('component_id', componentId)

      if (error) {
        logger.error('ReportService', 'deleteComponent', `Failed to delete component ${componentId}`, error)
        throw new Error(`Failed to delete component: ${error.message}`)
      }

      logger.info('ReportService', 'deleteComponent', `Successfully deleted component ${componentId}`)
    } catch (error) {
      logger.error('ReportService', 'deleteComponent', `Error deleting component ${componentId}`, error as Error)
      throw error
    }
  }

  async loadComponents(documentId: string): Promise<ReportComponent[]> {
    logger.info('ReportService', 'loadComponents', `Loading components for report ${documentId}`)
    
    try {
      const { data: components, error } = await this.supabase
        .from('report_components')
        .select('*')
        .eq('document_id', documentId)
        .order('created_at', { ascending: true })

      if (error) {
        logger.error('ReportService', 'loadComponents', `Failed to load components for report ${documentId}`, error)
        throw new Error(`Failed to load components: ${error.message}`)
      }

      const reportComponents = (components || []).map(c => c.data as ReportComponent)
      logger.info('ReportService', 'loadComponents', `Successfully loaded ${reportComponents.length} components`)
      return reportComponents
    } catch (error) {
      logger.error('ReportService', 'loadComponents', `Error loading components for report ${documentId}`, error as Error)
      throw error
    }
  }
}

export const reportService = new ReportService()