interface TimeoutManager {
  set(key: string, callback: () => void, delay: number): void
  clear(key: string): void
  clearAll(): void
  has(key: string): boolean
}

class TimeoutRegistry implements TimeoutManager {
  private timeouts = new Map<string, NodeJS.Timeout>()

  set(key: string, callback: () => void, delay: number): void {
    // Clear existing timeout with same key
    this.clear(key)
    
    const timeoutId = setTimeout(() => {
      this.timeouts.delete(key)
      callback()
    }, delay)
    
    this.timeouts.set(key, timeoutId)
  }

  clear(key: string): void {
    const timeoutId = this.timeouts.get(key)
    if (timeoutId) {
      clearTimeout(timeoutId)
      this.timeouts.delete(key)
    }
  }

  clearAll(): void {
    this.timeouts.forEach(timeoutId => clearTimeout(timeoutId))
    this.timeouts.clear()
  }

  has(key: string): boolean {
    return this.timeouts.has(key)
  }

  get size(): number {
    return this.timeouts.size
  }
}

// Debounce utility
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

// Throttle utility
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastExecution = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastExecution >= delay) {
      lastExecution = now
      func(...args)
    }
  }
}

// Standard timeout delays used across the editor
export const TIMEOUT_DELAYS = {
  IMMEDIATE: 0,
  QUICK: 10,
  DEBOUNCE: 50,
  MEDIUM: 100,
  SLOW: 250,
  INITIALIZATION: 1000
} as const

export { TimeoutRegistry }
export type { TimeoutManager }