import { logger } from './logger'

interface PerformanceEntry {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  metadata?: Record<string, any>
}

interface PerformanceMetrics {
  renderCount: number
  lastRenderTime: number
  averageRenderTime: number
  componentCount: number
  memoryUsage?: number
  componentRegistrations: number
  groupUpdates: number
  dependencyResolutions: number
}

class PerformanceMonitor {
  private entries = new Map<string, PerformanceEntry>()
  private metrics: PerformanceMetrics = {
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    componentCount: 0,
    componentRegistrations: 0,
    groupUpdates: 0,
    dependencyResolutions: 0
  }
  private renderTimes: number[] = []
  private isEnabled = process.env.NODE_ENV === 'development'

  /**
   * Start timing a performance event
   */
  startTiming(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return

    const entry: PerformanceEntry = {
      name,
      startTime: performance.now(),
      metadata
    }
    
    this.entries.set(name, entry)
    
    logger.debug('PerformanceMonitor', 'startTiming', `Started timing: ${name}`)
  }

  /**
   * End timing a performance event
   */
  endTiming(name: string): number | null {
    if (!this.isEnabled) return null

    const entry = this.entries.get(name)
    if (!entry) {
      logger.warn('PerformanceMonitor', 'endTiming', `No start entry found for: ${name}`)
      return null
    }

    entry.endTime = performance.now()
    entry.duration = entry.endTime - entry.startTime

    logger.debug('PerformanceMonitor', 'endTiming', 
      `Completed timing: ${name} - ${entry.duration.toFixed(2)}ms`)

    // Log slow operations
    if (entry.duration > 100) {
      logger.warn('PerformanceMonitor', 'endTiming', 
        `Slow operation detected: ${name} took ${entry.duration.toFixed(2)}ms`)
    }

    this.entries.delete(name)
    return entry.duration
  }

  /**
   * Time a function execution
   */
  timeFunction<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
    if (!this.isEnabled) return fn()

    this.startTiming(name, metadata)
    try {
      const result = fn()
      return result
    } finally {
      this.endTiming(name)
    }
  }

  /**
   * Time an async function execution
   */
  async timeAsync<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> {
    if (!this.isEnabled) return fn()

    this.startTiming(name, metadata)
    try {
      const result = await fn()
      return result
    } finally {
      this.endTiming(name)
    }
  }

  /**
   * Record a render event
   */
  recordRender(componentName: string, duration?: number): void {
    if (!this.isEnabled) return

    this.metrics.renderCount++
    
    if (duration) {
      this.metrics.lastRenderTime = duration
      this.renderTimes.push(duration)
      
      // Keep only last 100 render times for average calculation
      if (this.renderTimes.length > 100) {
        this.renderTimes.shift()
      }
      
      this.metrics.averageRenderTime = 
        this.renderTimes.reduce((sum, time) => sum + time, 0) / this.renderTimes.length
    }

    logger.debug('PerformanceMonitor', 'recordRender', 
      `Render #${this.metrics.renderCount}: ${componentName}${duration ? ` (${duration.toFixed(2)}ms)` : ''}`)
  }

  /**
   * Record component registration
   */
  recordComponentRegistration(componentId: string, componentType: string): void {
    if (!this.isEnabled) return

    this.metrics.componentRegistrations++
    this.metrics.componentCount++

    logger.debug('PerformanceMonitor', 'recordComponentRegistration', 
      `Component registered: ${componentType}#${componentId} (total: ${this.metrics.componentCount})`)
  }

  /**
   * Record component removal
   */
  recordComponentRemoval(componentId: string): void {
    if (!this.isEnabled) return

    this.metrics.componentCount = Math.max(0, this.metrics.componentCount - 1)

    logger.debug('PerformanceMonitor', 'recordComponentRemoval', 
      `Component removed: ${componentId} (remaining: ${this.metrics.componentCount})`)
  }

  /**
   * Record group status update
   */
  recordGroupUpdate(groupId: string, newStatus: string): void {
    if (!this.isEnabled) return

    this.metrics.groupUpdates++

    logger.debug('PerformanceMonitor', 'recordGroupUpdate', 
      `Group status updated: ${groupId} -> ${newStatus} (total updates: ${this.metrics.groupUpdates})`)
  }

  /**
   * Record dependency resolution
   */
  recordDependencyResolution(componentId: string, dependencies: string[]): void {
    if (!this.isEnabled) return

    this.metrics.dependencyResolutions++

    logger.debug('PerformanceMonitor', 'recordDependencyResolution', 
      `Dependencies resolved for ${componentId}: [${dependencies.join(', ')}]`)
  }

  /**
   * Update memory usage metrics
   */
  updateMemoryUsage(): void {
    if (!this.isEnabled) return

    if ('memory' in performance) {
      this.metrics.memoryUsage = (performance as any).memory.usedJSHeapSize
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    this.updateMemoryUsage()
    return { ...this.metrics }
  }

  /**
   * Get performance summary
   */
  getSummary(): string {
    const metrics = this.getMetrics()
    
    return `Performance Summary:
  Renders: ${metrics.renderCount} (avg: ${metrics.averageRenderTime.toFixed(2)}ms)
  Components: ${metrics.componentCount} (registered: ${metrics.componentRegistrations})
  Group Updates: ${metrics.groupUpdates}
  Dependency Resolutions: ${metrics.dependencyResolutions}
  Memory Usage: ${metrics.memoryUsage ? `${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB` : 'N/A'}`
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.entries.clear()
    this.renderTimes = []
    this.metrics = {
      renderCount: 0,
      lastRenderTime: 0,
      averageRenderTime: 0,
      componentCount: 0,
      componentRegistrations: 0,
      groupUpdates: 0,
      dependencyResolutions: 0
    }
    
    logger.info('PerformanceMonitor', 'reset', 'Performance metrics reset')
  }

  /**
   * Log performance summary
   */
  logSummary(): void {
    if (!this.isEnabled) return

    logger.info('PerformanceMonitor', 'logSummary', this.getSummary())
  }

  /**
   * Enable/disable performance monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    logger.info('PerformanceMonitor', 'setEnabled', `Performance monitoring ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Check for performance issues
   */
  checkPerformanceIssues(): string[] {
    const issues: string[] = []
    const metrics = this.getMetrics()

    // Check for excessive re-renders
    if (metrics.renderCount > 100 && metrics.averageRenderTime > 16) {
      issues.push(`High render count (${metrics.renderCount}) with slow average render time (${metrics.averageRenderTime.toFixed(2)}ms)`)
    }

    // Check for too many components
    if (metrics.componentCount > 50) {
      issues.push(`High component count (${metrics.componentCount}) may impact performance`)
    }

    // Check for excessive group updates
    if (metrics.groupUpdates > metrics.componentRegistrations * 2) {
      issues.push(`Excessive group updates (${metrics.groupUpdates}) relative to component registrations (${metrics.componentRegistrations})`)
    }

    // Check memory usage (if available)
    if (metrics.memoryUsage && metrics.memoryUsage > 100 * 1024 * 1024) { // 100MB
      issues.push(`High memory usage (${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB)`)
    }

    return issues
  }

  /**
   * Create a performance mark for browser dev tools
   */
  mark(name: string): void {
    if (!this.isEnabled) return

    try {
      performance.mark(`editor-${name}`)
    } catch (error) {
      logger.debug('PerformanceMonitor', 'mark', `Failed to create performance mark: ${name}`)
    }
  }

  /**
   * Measure between two performance marks
   */
  measure(name: string, startMark: string, endMark: string): void {
    if (!this.isEnabled) return

    try {
      performance.measure(`editor-${name}`, `editor-${startMark}`, `editor-${endMark}`)
    } catch (error) {
      logger.debug('PerformanceMonitor', 'measure', `Failed to create performance measure: ${name}`)
    }
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// React hook for component performance monitoring
export function usePerformanceMonitor(componentName: string) {
  const React = require('react')
  
  const renderCount = React.useRef(0)
  const lastRenderTime = React.useRef(performance.now())

  React.useEffect(() => {
    renderCount.current++
    const now = performance.now()
    const renderDuration = now - lastRenderTime.current
    
    performanceMonitor.recordRender(componentName, renderDuration)
    lastRenderTime.current = now
  })

  React.useEffect(() => {
    return () => {
      // Component unmounting
      performanceMonitor.recordComponentRemoval(componentName)
    }
  }, [componentName])

  return {
    startTiming: (name: string) => performanceMonitor.startTiming(`${componentName}-${name}`),
    endTiming: (name: string) => performanceMonitor.endTiming(`${componentName}-${name}`),
    timeFunction: <T>(name: string, fn: () => T) => 
      performanceMonitor.timeFunction(`${componentName}-${name}`, fn),
    timeAsync: <T>(name: string, fn: () => Promise<T>) => 
      performanceMonitor.timeAsync(`${componentName}-${name}`, fn)
  }
}

// Performance decorator for methods
export function performanceDecorator(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = function (...args: any[]) {
    const className = this.constructor.name
    const methodName = `${className}.${propertyKey}`
    
    return performanceMonitor.timeFunction(methodName, () => {
      return originalMethod.apply(this, args)
    })
  }

  return descriptor
}

// Performance decorator for async methods
export function asyncPerformanceDecorator(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = async function (...args: any[]) {
    const className = this.constructor.name
    const methodName = `${className}.${propertyKey}`
    
    return performanceMonitor.timeAsync(methodName, () => {
      return originalMethod.apply(this, args)
    })
  }

  return descriptor
}