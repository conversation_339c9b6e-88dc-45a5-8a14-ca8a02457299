import React from 'react'
import { Node } from '@tiptap/core'
import { <PERSON>de<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@tiptap/react'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    tableOfContents: {
      setTableOfContents: () => ReturnType
      removeExtraTableOfContents: () => ReturnType
    }
  }
}

/* ------------------------------------------------------------------
 *  Types
 * ------------------------------------------------------------------*/
interface HeadingItem {
  id: string
  level: number
  text: string
  anchor: string
}

interface TableOfContentsComponentProps {
  node: any
  editor?: any
}

/* ------------------------------------------------------------------
 *  Table of Contents Component
 * ------------------------------------------------------------------*/
const TableOfContentsComponent = React.memo<TableOfContentsComponentProps>(({
  node,
  editor
}) => {
  const [headings, setHeadings] = React.useState<HeadingItem[]>([])
  const updateTimeoutRef = React.useRef<NodeJS.Timeout | null>(null)

  // Update headings when document changes
  React.useEffect(() => {
    if (!editor) return

    const extractHeadings = () => {
      const headingNodes: HeadingItem[] = []
      const doc = editor.state.doc
      const seenHeadings = new Set<string>()

      doc.descendants((node: any, pos: number) => {
        if (node.type.name === 'heading') {
          const level = node.attrs.level
          const text = node.textContent
          
          if (text.trim()) {
            // Generate anchor from text
            const anchor = text
              .toLowerCase()
              .replace(/[^a-z0-9\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-')
              .trim()
              .replace(/^-+|-+$/g, '')
            
            // Create unique key for deduplication
            const key = `${text.trim()}-${level}-${pos}`
            
            // Only add if we haven't seen this heading at this position
            if (!seenHeadings.has(key)) {
              seenHeadings.add(key)
              headingNodes.push({
                id: `heading-${pos}`,
                level,
                text: text.trim(),
                anchor: anchor || `heading-${pos}`,
              })
            }
          }
        }
      })

      return headingNodes
    }

    const updateHeadings = () => {
      // Clear any pending update
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current)
      }

      // Debounce the update to prevent excessive re-renders
      updateTimeoutRef.current = setTimeout(() => {
        const newHeadings = extractHeadings()
        setHeadings(newHeadings)
      }, 300)
    }

    // Initial update (immediate)
    const newHeadings = extractHeadings()
    setHeadings(newHeadings)

    // Listen for document updates
    editor.on('update', updateHeadings)
    
    return () => {
      editor.off('update', updateHeadings)
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current)
      }
    }
  }, [editor])

  // Scroll to heading when clicked
  const scrollToHeading = (anchor: string) => {
    // First try to find an element with the exact anchor id
    let element = document.getElementById(anchor)
    
    // If not found, try to find a heading with this text
    if (!element) {
      const headingSelectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
      for (const selector of headingSelectors) {
        const headingElements = document.querySelectorAll(selector)
        for (const heading of headingElements) {
          if (heading.textContent?.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-') === anchor) {
            element = heading as HTMLElement
            break
          }
        }
        if (element) break
      }
    }
    
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  // Render nested table of contents
  const renderTOC = (items: HeadingItem[]) => {
    if (items.length === 0) {
      return (
        <div className="text-neutral-500 italic py-4">
          No headings found. Add some headings to see them in the table of contents.
        </div>
      )
    }

    return (
      <nav className="space-y-1">
        {items.map((heading) => {
          const indent = Math.max(0, (heading.level - 1)) * 16 // 16px per level, starting from 0
          
          return (
            <div
              key={heading.id}
              className="group"
              style={{ paddingLeft: `${indent}px` }}
            >
              <button
                onClick={() => scrollToHeading(heading.anchor)}
                className="text-left w-full py-1 px-2 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors duration-200 text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-neutral-100"
                title={`Go to "${heading.text}"`}
              >
                <span className={`
                  block truncate
                  ${heading.level === 1 ? 'font-semibold text-base' : ''}
                  ${heading.level === 2 ? 'font-medium text-sm' : ''}
                  ${heading.level >= 3 ? 'font-normal text-sm' : ''}
                `}>
                  {heading.text}
                </span>
              </button>
            </div>
          )
        })}
      </nav>
    )
  }

  return (
    <NodeViewWrapper className="table-of-contents-wrapper">
      <div className="glass-card p-6 my-4">
        <div className="flex items-center gap-2 mb-4 pb-2 border-b border-neutral-200 dark:border-neutral-700">
          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
          <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
            Table of Contents
          </h3>
        </div>
        
        {renderTOC(headings)}
      </div>
    </NodeViewWrapper>
  )
})

TableOfContentsComponent.displayName = 'TableOfContentsComponent'

/* ------------------------------------------------------------------
 *  TipTap Table of Contents Extension
 * ------------------------------------------------------------------*/
export const TableOfContentsExtension = Node.create({
  name: 'tableOfContents',

  group: 'block',

  atom: true,

  onCreate() {
    // Clean up duplicate table of contents on load
    setTimeout(() => {
      if (this.editor && this.editor.commands.removeExtraTableOfContents) {
        this.editor.commands.removeExtraTableOfContents()
      }
    }, 100)
  },

  addAttributes() {
    return {
      id: {
        default: 'table-of-contents',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="table-of-contents"]',
        getAttrs: (element) => {
          if (typeof element === 'string') return false
          return {
            id: element.getAttribute('id') || 'table-of-contents',
          }
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', { 'data-type': 'table-of-contents', ...HTMLAttributes }]
  },

  addNodeView() {
    return ReactNodeViewRenderer((props) => (
      <TableOfContentsComponent
        {...props}
        editor={props.editor}
      />
    ))
  },

  addCommands() {
    return {
      setTableOfContents:
        () =>
        ({ commands, state }: { commands: any; state: any }) => {
          // Check if a table of contents already exists
          let tocExists = false
          state.doc.descendants((node: any) => {
            if (node.type.name === 'tableOfContents') {
              tocExists = true
              return false // Stop searching
            }
          })

          // If one already exists, don't add another
          if (tocExists) {
            console.warn('Table of Contents already exists in document')
            return false
          }

          return commands.insertContent({
            type: this.name,
          })
        },
      removeExtraTableOfContents:
        () =>
        ({ state, tr }: { state: any; tr: any }) => {
          // Find all table of contents nodes
          const tocNodes: Array<{ pos: number; node: any }> = []
          
          state.doc.descendants((node: any, pos: number) => {
            if (node.type.name === 'tableOfContents') {
              tocNodes.push({ pos, node })
            }
          })

          // Keep only the first one, remove the rest
          if (tocNodes.length > 1) {
            // Sort by position (descending) to remove from end to start
            tocNodes.sort((a, b) => b.pos - a.pos)
            
            // Remove all but the first one
            for (let i = 1; i < tocNodes.length; i++) {
              const { pos, node } = tocNodes[i]
              tr.delete(pos, pos + node.nodeSize)
            }
            
            return true
          }
          
          return false
        },
    }
  },
})

/* ------------------------------------------------------------------
 *  Export types for use in other components
 * ------------------------------------------------------------------*/
export type { TableOfContentsComponentProps, HeadingItem }