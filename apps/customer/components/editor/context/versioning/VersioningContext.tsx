import React, { create<PERSON>ontext, useContext, useReducer, useCallback, useMemo, useRef } from 'react'
import { Editor } from '@tiptap/react'
import { versionService, logger, debounce, TIMEOUT_DELAYS } from '../../services'
import type { DocumentVersion } from '../../services'

interface VersioningState {
  versions: DocumentVersion[]
  currentVersion: DocumentVersion | null
  isLoading: boolean
  error: string | null
  autoSaveEnabled: boolean
  lastAutoSave: Date | null
}

type VersioningAction =
  | { type: 'SET_VERSIONS'; payload: DocumentVersion[] }
  | { type: 'ADD_VERSION'; payload: DocumentVersion }
  | { type: 'SET_CURRENT_VERSION'; payload: DocumentVersion | null }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_AUTO_SAVE_ENABLED'; payload: boolean }
  | { type: 'SET_LAST_AUTO_SAVE'; payload: Date }
  | { type: 'REMOVE_VERSION'; payload: number }
  | { type: 'RESET_VERSIONING' }

const initialState: VersioningState = {
  versions: [],
  currentVersion: null,
  isLoading: false,
  error: null,
  autoSaveEnabled: true,
  lastAutoSave: null
}

function versioningReducer(state: VersioningState, action: VersioningAction): VersioningState {
  switch (action.type) {
    case 'SET_VERSIONS':
      return { ...state, versions: action.payload }
    
    case 'ADD_VERSION':
      return { 
        ...state, 
        versions: [action.payload, ...state.versions]
      }
    
    case 'SET_CURRENT_VERSION':
      return { ...state, currentVersion: action.payload }
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    
    case 'SET_AUTO_SAVE_ENABLED':
      return { ...state, autoSaveEnabled: action.payload }
    
    case 'SET_LAST_AUTO_SAVE':
      return { ...state, lastAutoSave: action.payload }
    
    case 'REMOVE_VERSION':
      return {
        ...state,
        versions: state.versions.filter(v => v.version_number !== action.payload)
      }
    
    case 'RESET_VERSIONING':
      return initialState
    
    default:
      return state
  }
}

interface VersioningOperations {
  createVersion: (changeSummary?: string) => Promise<DocumentVersion | null>
  createAutoSaveVersion: () => Promise<DocumentVersion | null>
  loadVersion: (versionNumber: number) => Promise<DocumentVersion | null>
  listVersions: () => Promise<void>
  deleteVersion: (versionNumber: number) => Promise<void>
  restoreVersion: (versionNumber: number) => Promise<void>
  setAutoSaveEnabled: (enabled: boolean) => void
  cleanupOldAutoSaves: (keepCount?: number) => Promise<void>
}

interface VersioningContextValue extends VersioningOperations {
  versions: DocumentVersion[]
  currentVersion: DocumentVersion | null
  isLoading: boolean
  error: string | null
  autoSaveEnabled: boolean
  lastAutoSave: Date | null
}

const VersioningContext = createContext<VersioningContextValue | undefined>(undefined)

interface VersioningProviderProps {
  children: React.ReactNode
  documentId: string | null
  editor: Editor | null
  onContentRestore?: (content: string, data: any) => void
}

export function VersioningProvider({ 
  children, 
  documentId, 
  editor, 
  onContentRestore 
}: VersioningProviderProps) {
  const [state, dispatch] = useReducer(versioningReducer, initialState)
  
  // Debounced auto-save version creation
  const debouncedAutoSave = useRef(
    debounce(async () => {
      if (state.autoSaveEnabled && documentId && editor) {
        await createAutoSaveVersion()
      }
    }, TIMEOUT_DELAYS.SLOW)
  )

  const createVersion = useCallback(async (changeSummary?: string): Promise<DocumentVersion | null> => {
    if (!documentId || !editor) {
      logger.warn('VersioningProvider', 'createVersion', 'Cannot create version: missing documentId or editor')
      return null
    }

    dispatch({ type: 'SET_LOADING', payload: true })
    dispatch({ type: 'SET_ERROR', payload: null })

    try {
      logger.info('VersioningProvider', 'createVersion', `Creating version for document ${documentId}`)
      
      const content = editor.getHTML()
      const data = editor.getJSON()
      
      const version = await versionService.createVersion(documentId, content, data, {
        changeSummary,
        isAutoSave: false,
        createdBy: (editor as any).storage?.user?.id
      })

      dispatch({ type: 'ADD_VERSION', payload: version })
      dispatch({ type: 'SET_CURRENT_VERSION', payload: version })
      
      logger.info('VersioningProvider', 'createVersion', `Created version ${version.version_number}`)
      return version
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create version'
      logger.error('VersioningProvider', 'createVersion', 'Error creating version', error as Error)
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      return null
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [documentId, editor])

  const createAutoSaveVersion = useCallback(async (): Promise<DocumentVersion | null> => {
    if (!documentId || !editor || !state.autoSaveEnabled) {
      return null
    }

    try {
      logger.debug('VersioningProvider', 'createAutoSaveVersion', `Creating auto-save version for document ${documentId}`)
      
      const content = editor.getHTML()
      const data = editor.getJSON()
      
      const version = await versionService.createVersion(documentId, content, data, {
        isAutoSave: true,
        createdBy: (editor as any).storage?.user?.id
      })

      dispatch({ type: 'ADD_VERSION', payload: version })
      dispatch({ type: 'SET_LAST_AUTO_SAVE', payload: new Date() })
      
      // Clean up old auto-saves periodically
      if (version.version_number % 10 === 0) {
        await cleanupOldAutoSaves()
      }
      
      logger.debug('VersioningProvider', 'createAutoSaveVersion', `Created auto-save version ${version.version_number}`)
      return version
    } catch (error) {
      logger.error('VersioningProvider', 'createAutoSaveVersion', 'Error creating auto-save version', error as Error)
      return null
    }
  }, [documentId, editor, state.autoSaveEnabled])

  const loadVersion = useCallback(async (versionNumber: number): Promise<DocumentVersion | null> => {
    if (!documentId) {
      logger.warn('VersioningProvider', 'loadVersion', 'Cannot load version: missing documentId')
      return null
    }

    dispatch({ type: 'SET_LOADING', payload: true })
    dispatch({ type: 'SET_ERROR', payload: null })

    try {
      logger.info('VersioningProvider', 'loadVersion', `Loading version ${versionNumber} for document ${documentId}`)
      
      const version = await versionService.loadVersion(documentId, versionNumber)
      
      if (version) {
        dispatch({ type: 'SET_CURRENT_VERSION', payload: version })
        logger.info('VersioningProvider', 'loadVersion', `Loaded version ${versionNumber}`)
      } else {
        dispatch({ type: 'SET_ERROR', payload: `Version ${versionNumber} not found` })
      }
      
      return version
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load version'
      logger.error('VersioningProvider', 'loadVersion', `Error loading version ${versionNumber}`, error as Error)
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      return null
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [documentId])

  const listVersions = useCallback(async (): Promise<void> => {
    if (!documentId) {
      logger.warn('VersioningProvider', 'listVersions', 'Cannot list versions: missing documentId')
      return
    }

    dispatch({ type: 'SET_LOADING', payload: true })
    dispatch({ type: 'SET_ERROR', payload: null })

    try {
      logger.info('VersioningProvider', 'listVersions', `Loading versions for document ${documentId}`)
      
      const versions = await versionService.listVersions(documentId)
      dispatch({ type: 'SET_VERSIONS', payload: versions })
      
      logger.info('VersioningProvider', 'listVersions', `Loaded ${versions.length} versions`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load versions'
      logger.error('VersioningProvider', 'listVersions', 'Error loading versions', error as Error)
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [documentId])

  const deleteVersion = useCallback(async (versionNumber: number): Promise<void> => {
    if (!documentId) {
      logger.warn('VersioningProvider', 'deleteVersion', 'Cannot delete version: missing documentId')
      return
    }

    try {
      logger.info('VersioningProvider', 'deleteVersion', `Deleting version ${versionNumber} for document ${documentId}`)
      
      await versionService.deleteVersion(documentId, versionNumber)
      dispatch({ type: 'REMOVE_VERSION', payload: versionNumber })
      
      logger.info('VersioningProvider', 'deleteVersion', `Deleted version ${versionNumber}`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete version'
      logger.error('VersioningProvider', 'deleteVersion', `Error deleting version ${versionNumber}`, error as Error)
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      throw error
    }
  }, [documentId])

  const restoreVersion = useCallback(async (versionNumber: number): Promise<void> => {
    const version = await loadVersion(versionNumber)
    
    if (version && version.content && onContentRestore) {
      logger.info('VersioningProvider', 'restoreVersion', `Restoring version ${versionNumber}`)
      onContentRestore(version.content, version.data)
    }
  }, [loadVersion, onContentRestore])

  const setAutoSaveEnabled = useCallback((enabled: boolean) => {
    logger.info('VersioningProvider', 'setAutoSaveEnabled', `Auto-save ${enabled ? 'enabled' : 'disabled'}`)
    dispatch({ type: 'SET_AUTO_SAVE_ENABLED', payload: enabled })
  }, [])

  const cleanupOldAutoSaves = useCallback(async (keepCount: number = 10): Promise<void> => {
    if (!documentId) {
      return
    }

    try {
      logger.debug('VersioningProvider', 'cleanupOldAutoSaves', `Cleaning up old auto-saves for document ${documentId}`)
      await versionService.cleanupAutoSaveVersions(documentId, keepCount)
    } catch (error) {
      logger.error('VersioningProvider', 'cleanupOldAutoSaves', 'Error cleaning up auto-saves', error as Error)
    }
  }, [documentId])

  // Auto-save trigger effect
  React.useEffect(() => {
    if (state.autoSaveEnabled && editor) {
      const handleUpdate = () => {
        debouncedAutoSave.current()
      }

      editor.on('update', handleUpdate)
      return () => {
        editor.off('update', handleUpdate)
      }
    }
  }, [state.autoSaveEnabled, editor])

  // Load versions when documentId changes
  React.useEffect(() => {
    if (documentId) {
      listVersions()
    } else {
      dispatch({ type: 'RESET_VERSIONING' })
    }
  }, [documentId, listVersions])

  const contextValue = useMemo((): VersioningContextValue => ({
    versions: state.versions,
    currentVersion: state.currentVersion,
    isLoading: state.isLoading,
    error: state.error,
    autoSaveEnabled: state.autoSaveEnabled,
    lastAutoSave: state.lastAutoSave,
    createVersion,
    createAutoSaveVersion,
    loadVersion,
    listVersions,
    deleteVersion,
    restoreVersion,
    setAutoSaveEnabled,
    cleanupOldAutoSaves
  }), [
    state,
    createVersion,
    createAutoSaveVersion,
    loadVersion,
    listVersions,
    deleteVersion,
    restoreVersion,
    setAutoSaveEnabled,
    cleanupOldAutoSaves
  ])

  return (
    <VersioningContext.Provider value={contextValue}>
      {children}
    </VersioningContext.Provider>
  )
}

export function useVersioning(): VersioningContextValue {
  const context = useContext(VersioningContext)
  if (!context) {
    throw new Error('useVersioning must be used within a VersioningProvider')
  }
  return context
}