import { useRef, useCallback } from 'react'
import { ReportComponent } from '../DocumentContext'

interface DependencyManagerProps {
  stateRef: React.MutableRefObject<{ components: Map<string, ReportComponent> }>
}

export const useDependencyManager = ({ stateRef }: DependencyManagerProps) => {
  const dependencyWaitersRef = useRef<Map<string, Array<() => void>>>(new Map())

  // Check if dependencies are ready
  const areDependenciesReady = useCallback(
    (componentId: string): boolean => {
      const component = stateRef.current.components.get(componentId)
      if (!component?.dependencies || component.dependencies.length === 0) {
        console.log(`DependencyManager: areDependenciesReady(${componentId}): No dependencies, returning true`)
        return true
      }

      console.log(
        `DependencyManager: areDependenciesReady(${componentId}): Checking ${component.dependencies.length} dependencies`
      )

      const readyDependencies = component.dependencies.filter((depId) => {
        const dep = stateRef.current.components.get(depId)
        if (!dep) {
          console.log(`DependencyManager: areDependenciesReady(${componentId}): Dependency ${depId} not found`)
          return false
        }

        const finalStates = ['loaded', 'preserved', 'locked']
        const isReady = finalStates.includes(dep.status)
        console.log(
          `DependencyManager: areDependenciesReady(${componentId}): Dependency ${depId} (${dep.type}) status: ${dep.status}, ready: ${isReady}`
        )

        return isReady
      })

      const result = readyDependencies.length === component.dependencies.length
      console.log(
        `DependencyManager: areDependenciesReady(${componentId}): Ready deps: ${readyDependencies.length}/${component.dependencies.length}, Result: ${result}`
      )

      return result
    },
    [stateRef]
  )

  // Wait for dependencies to be ready
  const waitForDependencies = useCallback(
    async (componentId: string): Promise<void> => {
      console.log(`DependencyManager: waitForDependencies called for ${componentId}`)

      if (areDependenciesReady(componentId)) {
        console.log(`DependencyManager: Dependencies already ready for ${componentId}`)
        return Promise.resolve()
      }

      return new Promise((resolve) => {
        const component = stateRef.current.components.get(componentId)
        if (!component?.dependencies || component.dependencies.length === 0) {
          console.log(`DependencyManager: No dependencies for ${componentId}, resolving immediately`)
          resolve()
          return
        }

        console.log(
          `DependencyManager: Setting up waiters for ${componentId}, dependencies: ${component.dependencies.join(', ')}`
        )

        const waiterCallback = () => {
          if (areDependenciesReady(componentId)) {
            console.log(`DependencyManager: Dependencies now ready for ${componentId}, resolving`)
            resolve()
          }
        }

        // Set up waiters for each dependency that's not ready
        let waitersAdded = 0
        component.dependencies.forEach((depId) => {
          const dep = stateRef.current.components.get(depId)
          const finalStates = ['loaded', 'error', 'preserved', 'locked']
          if (dep && !finalStates.includes(dep.status)) {
            if (!dependencyWaitersRef.current.has(depId)) {
              dependencyWaitersRef.current.set(depId, [])
            }
            dependencyWaitersRef.current.get(depId)!.push(waiterCallback)
            waitersAdded++
            console.log(`DependencyManager: Added waiter for ${depId} (status: ${dep.status})`)
          }
        })

        // Check again in case dependencies resolved while setting up waiters
        if (areDependenciesReady(componentId)) {
          console.log(`DependencyManager: Dependencies became ready while setting up waiters for ${componentId}`)
          resolve()
        } else if (waitersAdded === 0) {
          console.warn(`DependencyManager: No waiters added for ${componentId} but dependencies not ready`)
          resolve()
        }
      })
    },
    [stateRef, areDependenciesReady]
  )

  // Notify waiters when a component reaches a final state
  const notifyDependencyWaiters = useCallback(
    (componentId: string) => {
      const waiters = dependencyWaitersRef.current.get(componentId)
      if (waiters) {
        console.log(`DependencyManager: Component ${componentId} reached final state, notifying ${waiters.length} waiters`)
        waiters.forEach((waiter) => waiter())
        dependencyWaitersRef.current.delete(componentId)
      }
    },
    []
  )

  return {
    areDependenciesReady,
    waitForDependencies,
    notifyDependencyWaiters
  }
}