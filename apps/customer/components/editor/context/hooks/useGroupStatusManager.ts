import { useRef, useCallback } from 'react'
import { ReportComponent, DocumentAction } from '../DocumentContext'

interface GroupStatusManagerProps {
  dispatch: React.Dispatch<DocumentAction>
  stateRef: React.MutableRefObject<{ components: Map<string, ReportComponent> }>
}

export const useGroupStatusManager = ({ dispatch, stateRef }: GroupStatusManagerProps) => {
  const groupStatusUpdateTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const pendingGroupUpdates = useRef<Set<string>>(new Set())

  // Helper function to get all descendants using a specific components map
  const getAllDescendantsFromState = useCallback((groupId: string, components: Map<string, ReportComponent>): ReportComponent[] => {
    const directChildren = Array.from(components.values()).filter((component) => component.parentId === groupId)
    const allDescendants: ReportComponent[] = [...directChildren]

    directChildren.forEach((child) => {
      if (child.type === 'report-group') {
        allDescendants.push(...getAllDescendantsFromState(child.id, components))
      }
    })

    return allDescendants
  }, [])

  // Forward declare for circular dependency
  let scheduleGroupStatusUpdate: (groupId: string, immediate?: boolean) => void

  // Internal group status management - performs the actual status update
  const updateGroupStatusInternal = useCallback(
    (groupId: string) => {
      console.log(`GroupStatusManager.updateGroupStatus: Called for ${groupId}`)

      const currentState = stateRef.current
      const group = currentState.components.get(groupId)
      if (!group) {
        console.log(`GroupStatusManager.updateGroupStatus: ${groupId} not found in components map`)
        return
      }
      if (group.type !== 'report-group') {
        console.log(`GroupStatusManager.updateGroupStatus: ${groupId} is not a report-group (type: ${group.type})`)
        return
      }

      // Skip if group is in a final state (locked/preserved)
      if (group.status === 'locked' || group.status === 'preserved') {
        console.log(`GroupStatusManager.updateGroupStatus: ${groupId} is ${group.status}, skipping`)
        return
      }

      // Get direct children first for debugging
      const directChildren = Array.from(currentState.components.values()).filter(
        (component) => component.parentId === groupId
      )
      
      console.log(
        `GroupStatusManager.updateGroupStatus: ${groupId} has ${directChildren.length} direct children:`,
        directChildren.map((c) => `${c.id}(${c.type}):${c.status}`)
      )

      // Get ALL descendants using current state
      const descendants = getAllDescendantsFromState(groupId, currentState.components)

      console.log(
        `GroupStatusManager.updateGroupStatus: ${groupId} has ${descendants.length} total descendants:`,
        descendants.map((c) => `${c.id}(${c.type}):${c.status}`)
      )

      if (descendants.length === 0) {
        if (group.status !== 'loaded') {
          console.log(`GroupStatusManager.updateGroupStatus: ${groupId} has no descendants, setting status to 'loaded'`)
          dispatch({ type: 'COMPONENT_UPDATED', id: groupId, updates: { status: 'loaded' } })
        }
        return
      }

      // Check ALL descendant status
      const hasError = descendants.some((descendant) => descendant.status === 'error')
      const hasIdleOrLoading = descendants.some(
        (descendant) => descendant.status === 'idle' || descendant.status === 'loading'
      )
      const allLoaded = descendants.every(
        (descendant) =>
          descendant.status === 'loaded' ||
          descendant.status === 'preserved' ||
          descendant.status === 'locked'
      )

      // Debug logging for status determination
      console.log(`GroupStatusManager.updateGroupStatus: ${groupId} status check:
        - Current group status: ${group.status}
        - Total descendants: ${descendants.length}
        - Has error: ${hasError}
        - Has idle/loading: ${hasIdleOrLoading}
        - All loaded: ${allLoaded}
        - Descendant statuses: ${descendants.map(d => `${d.id}:${d.status}`).join(', ')}`)

      let newStatus: ReportComponent['status']
      // IMPORTANT: If any children are still loading/idle, the group should be loading
      // This prevents the invalid state of an errored group with loading children
      if (hasIdleOrLoading) {
        newStatus = 'loading'
      } else if (hasError) {
        newStatus = 'error'
      } else if (allLoaded) {
        newStatus = 'loaded'
      } else {
        newStatus = 'loading' // Default to loading for any other state
      }

      console.log(`GroupStatusManager.updateGroupStatus: ${groupId} determined new status: ${newStatus}`)

      // Only update if status actually changed
      if (group.status !== newStatus) {
        console.log(
          `GroupStatusManager.updateGroupStatus: ${groupId} updating status from '${group.status}' to '${newStatus}' based on ${descendants.length} descendants`
        )
        dispatch({ type: 'COMPONENT_UPDATED', id: groupId, updates: { status: newStatus } })
        
        // After updating this group, check if its parent needs updating too
        if (group.parentId) {
          console.log(`GroupStatusManager.updateGroupStatus: ${groupId} status changed, scheduling parent ${group.parentId} update`)
          scheduleGroupStatusUpdate(group.parentId, false)
        }
      }
    },
    [dispatch, getAllDescendantsFromState, stateRef]
  )

  // Debounced group status update function
  scheduleGroupStatusUpdate = useCallback((groupId: string, immediate = false) => {
    console.log(`GroupStatusManager.scheduleGroupStatusUpdate: Scheduling update for ${groupId}, immediate: ${immediate}`)

    // Prevent scheduling if already pending
    if (pendingGroupUpdates.current.has(groupId)) {
      console.log(`GroupStatusManager.scheduleGroupStatusUpdate: Update already pending for ${groupId}, skipping`)
      return
    }

    // Clear existing timeout for this group
    const existingTimeout = groupStatusUpdateTimeouts.current.get(groupId)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
      groupStatusUpdateTimeouts.current.delete(groupId)
    }

    // Add to pending updates
    pendingGroupUpdates.current.add(groupId)

    if (immediate) {
      requestAnimationFrame(() => {
        if (pendingGroupUpdates.current.has(groupId)) {
          updateGroupStatusInternal(groupId)
          pendingGroupUpdates.current.delete(groupId)
        }
      })
    } else {
      // Debounce the update
      const timeout = setTimeout(() => {
        if (pendingGroupUpdates.current.has(groupId)) {
          updateGroupStatusInternal(groupId)
          pendingGroupUpdates.current.delete(groupId)
        }
        groupStatusUpdateTimeouts.current.delete(groupId)
      }, 100) // Increased debounce delay to 100ms

      groupStatusUpdateTimeouts.current.set(groupId, timeout)
    }
  }, [updateGroupStatusInternal])

  // Public group status update function
  const updateGroupStatus = useCallback(
    (groupId: string, immediate = false) => {
      scheduleGroupStatusUpdate(groupId, immediate)
    },
    [scheduleGroupStatusUpdate]
  )

  // Force update all group statuses
  const updateAllGroupStatuses = useCallback(() => {
    console.log('GroupStatusManager.updateAllGroupStatuses: Updating all group statuses')
    const groups = Array.from(stateRef.current.components.values()).filter((comp) => comp.type === 'report-group')
    groups.forEach((group) => {
      updateGroupStatus(group.id, false)
    })
  }, [updateGroupStatus, stateRef])

  // Refresh all descendants of a group
  const refreshAllDescendants = useCallback(
    (groupId: string) => {
      console.log(`GroupStatusManager.refreshAllDescendants: Refreshing all descendants of group ${groupId}`)

      const group = stateRef.current.components.get(groupId)
      if (!group || group.type !== 'report-group') {
        console.warn(`GroupStatusManager.refreshAllDescendants: ${groupId} is not a valid report group`)
        return
      }

      const descendants = getAllDescendantsFromState(groupId, stateRef.current.components)
      console.log(
        `GroupStatusManager.refreshAllDescendants: Found ${descendants.length} descendants to refresh:`,
        descendants.map((d) => `${d.id}(${d.type})`)
      )

      // Set all descendants to 'idle' status to trigger refresh
      descendants.forEach((descendant) => {
        if (descendant.status !== 'locked' && descendant.status !== 'preserved') {
          console.log(`GroupStatusManager.refreshAllDescendants: Setting ${descendant.id} (${descendant.type}) status to 'idle'`)
          dispatch({
            type: 'COMPONENT_UPDATED',
            id: descendant.id,
            updates: { status: 'idle' }
          })
        }
      })

      // Also set the group itself to 'loading' to indicate refresh is in progress
      if (group.status !== 'locked' && group.status !== 'preserved') {
        console.log(`GroupStatusManager.refreshAllDescendants: Setting group ${groupId} status to 'loading'`)
        dispatch({
          type: 'COMPONENT_UPDATED',
          id: groupId,
          updates: { status: 'loading' }
        })
      }
    },
    [getAllDescendantsFromState, dispatch, stateRef]
  )

  // Cleanup function
  const cleanup = useCallback(() => {
    groupStatusUpdateTimeouts.current.forEach((timeout) => clearTimeout(timeout))
    groupStatusUpdateTimeouts.current.clear()
    pendingGroupUpdates.current.clear()
  }, [])

  return {
    updateGroupStatus,
    updateAllGroupStatuses,
    refreshAllDescendants,
    cleanup,
    getAllDescendantsFromState
  }
}