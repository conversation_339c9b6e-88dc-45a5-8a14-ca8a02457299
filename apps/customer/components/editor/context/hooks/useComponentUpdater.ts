import { useRef, useCallback } from 'react'
import { Editor } from '@tiptap/react'
import { createClient } from '@/app/supabase/client'
import { ReportComponent, DocumentAction } from '../DocumentContext'

interface ComponentUpdaterProps {
  dispatch: React.Dispatch<DocumentAction>
  stateRef: React.MutableRefObject<{ 
    components: Map<string, ReportComponent>
    editor: Editor | null
  }>
  documentId: string
  onSave?: (content: string, data?: any) => Promise<void> | void
  updateGroupStatus: (groupId: string, immediate?: boolean) => void
  triggerAutoSaveVersion: () => Promise<void>
  notifyDependencyWaiters: (componentId: string) => void
}

export const useComponentUpdater = ({ 
  dispatch, 
  stateRef, 
  documentId, 
  onSave, 
  updateGroupStatus, 
  triggerAutoSaveVersion,
  notifyDependencyWaiters 
}: ComponentUpdaterProps) => {
  // Track components currently being updated to prevent infinite loops
  const updatingComponentsRef = useRef<Set<string>>(new Set())

  const updateComponent = useCallback(
    (id: string, updates: Partial<ReportComponent>) => {
      // Prevent recursive updates for the same component
      if (updatingComponentsRef.current.has(id)) {
        console.log(`ComponentUpdater: Preventing recursive update for component ${id}`)
        return
      }

      const existing = stateRef.current.components.get(id)

      // Only proceed if there are actual changes
      const hasChanges = Object.keys(updates).some(key => {
        const updateKey = key as keyof ReportComponent
        return existing?.[updateKey] !== updates[updateKey]
      })

      if (!hasChanges && Object.keys(updates).length > 0) {
        console.log(`ComponentUpdater: No actual changes for component ${id}, skipping update`)
        return
      }

      // Mark component as being updated
      updatingComponentsRef.current.add(id)

      try {
        dispatch({ type: 'COMPONENT_UPDATED', id, updates })

        // If component just finished loading or reached any final state, notify any waiters
        if (existing) {
          const finalStates = ['loaded', 'error', 'preserved', 'locked']
          if (!finalStates.includes(existing.status) && updates.status && finalStates.includes(updates.status)) {
            console.log(`ComponentUpdater: Component ${id} reached final state ${updates.status}, notifying waiters`)
            notifyDependencyWaiters(id)
          }

          // Handle status changes
          if (updates.status && existing.status !== updates.status) {
            handleStatusChange(id, existing, updates)
          }
        }
      } finally {
        // Always remove from updating set
        updatingComponentsRef.current.delete(id)
      }
    },
    [dispatch, stateRef, updateGroupStatus, triggerAutoSaveVersion, notifyDependencyWaiters, documentId, onSave]
  )

  const handleStatusChange = useCallback(
    (id: string, existing: ReportComponent, updates: Partial<ReportComponent>) => {
      console.log(`[STATUS_CHANGE] ComponentUpdater: Component ${id} status changed from '${existing.status}' to '${updates.status}'`)

      // Send state change messages to parent groups and dependents
      if (updates.status) {
        const updatedComponent = { ...existing, ...updates }
        
        // Notify parent groups with current state snapshot
        if (existing.parentId) {
          const parent = stateRef.current.components.get(existing.parentId)
          if (parent && parent.type === 'report-group') {
            // Get all children of the parent with their current states
            const allChildrenStates = Array.from(stateRef.current.components.values())
              .filter(comp => comp.parentId === existing.parentId)
              .map(comp => comp.id === id ? updatedComponent : comp) // Use updated state for this component
            
            console.log(`ComponentUpdater: Sending CHILD_STATE_CHANGED to parent ${existing.parentId} with ${allChildrenStates.length} children`)
            dispatch({
              type: 'CHILD_STATE_CHANGED',
              parentId: existing.parentId,
              childState: updatedComponent,
              allChildrenStates
            })
          }
        }

        // Notify dependent summaries with current state snapshot
        const dependentSummaries = Array.from(stateRef.current.components.values())
          .filter(comp => comp.type === 'report-summary' && comp.dependencies?.includes(id))
        
        dependentSummaries.forEach(summary => {
          // Get all dependencies of the summary with their current states
          const allDependencyStates = (summary.dependencies || [])
            .map(depId => {
              const dep = stateRef.current.components.get(depId)
              return depId === id ? updatedComponent : dep
            })
            .filter(Boolean) as ReportComponent[]
          
          console.log(`ComponentUpdater: Sending DEPENDENCY_STATE_CHANGED to summary ${summary.id} with ${allDependencyStates.length} dependencies`)
          dispatch({
            type: 'DEPENDENCY_STATE_CHANGED',
            dependentId: summary.id,
            dependencyState: updatedComponent,
            allDependencyStates
          })
        })
      }

      // Handle component reaching 'loaded' state
      if (updates.status === 'loaded') {
        handleComponentLoaded(id, existing)
      }

      // Handle component moving from 'loaded' to 'idle'
      if (existing.status === 'loaded' && updates.status === 'idle') {
        handleComponentReset(id, existing)
      }

      // Handle section starting to load
      if (existing.type === 'report-section' && updates.status === 'loading') {
        handleSectionStartLoading(id, existing)
      }

      // Trigger document save
      triggerDocumentSave()
    },
    [stateRef, dispatch, documentId, onSave, triggerAutoSaveVersion]
  )

  const handleComponentLoaded = useCallback(
    (id: string, existing: ReportComponent) => {
      console.log(`ComponentUpdater: Component ${id} reached 'loaded' state, triggering immediate save`)

      // Update parent groups with debouncing to prevent cascading updates
      if (existing.parentId) {
        console.log(`ComponentUpdater: Component ${id} loaded, scheduling parent group ${existing.parentId} update`)
        updateGroupStatus(existing.parentId, false) // Use debounced update
      }

      // Also check for containing groups
      const containingGroups = Array.from(stateRef.current.components.values()).filter(
        (comp) => comp.type === 'report-group' && comp.children?.includes(id)
      )

      containingGroups.forEach((group) => {
        console.log(`ComponentUpdater: Component ${id} loaded, scheduling containing group ${group.id} update`)
        updateGroupStatus(group.id, false) // Use debounced update
      })

      // Trigger immediate save
      setTimeout(() => {
        if (stateRef.current.editor) {
          dispatch({
            type: 'CONTENT_CHANGED',
            content: stateRef.current.editor.getHTML(),
            data: stateRef.current.editor.getJSON(),
            source: 'system',
          })

          if (onSave) {
            try {
              onSave(stateRef.current.editor.getHTML(), stateRef.current.editor.getJSON())
            } catch (error) {
              console.error('ComponentUpdater: Error in onSave callback:', error)
            }
          }
        }
      }, 50)

      // Check if all components are now loaded - use longer delay to prevent too frequent checks
      setTimeout(() => {
        const allComponents = Array.from(stateRef.current.components.values())
        const allComponentsLoaded = allComponents.every(component => {
          const finalStates = ['loaded', 'preserved', 'locked', 'error']
          return finalStates.includes(component.status)
        })

        if (allComponentsLoaded && allComponents.length > 0) {
          console.log('ComponentUpdater: All components are now loaded, triggering auto-save version')

          // Force update all group statuses - this ensures groups with error status are re-evaluated
          const groups = allComponents.filter((comp) => comp.type === 'report-group')
          
          // Use requestAnimationFrame to batch the updates
          requestAnimationFrame(() => {
            groups.forEach((group) => {
              // Only update groups that are in error state
              if (group.status === 'error') {
                console.log(`ComponentUpdater: Force updating error group ${group.id}`)
                updateGroupStatus(group.id, true)
              }
            })
          })

          triggerAutoSaveVersion().catch(error => {
            console.error('ComponentUpdater: Error triggering auto-save version:', error)
          })
        }
      }, 500) // Increased delay to 500ms
    },
    [stateRef, updateGroupStatus, dispatch, onSave, triggerAutoSaveVersion]
  )

  const handleComponentReset = useCallback(
    async (id: string, existing: ReportComponent) => {
      console.log(`ComponentUpdater: Component ${id} moved from 'loaded' to 'idle', creating document version`)
      
      const componentType = existing?.type || 'component'
      const componentTitle = existing?.title || id
      const changeSummary = `${componentType} "${componentTitle}" was reset from loaded to idle state`

      setTimeout(async () => {
        if (stateRef.current.editor && documentId) {
          try {
            const supabase = createClient()
            const content = stateRef.current.editor.getHTML()
            const data = stateRef.current.editor.getJSON()

            const { data: versions, error: versionError } = await supabase
              .from('document_versions')
              .select('version_number')
              .eq('document_id', documentId)
              .order('version_number', { ascending: false })
              .limit(1)

            if (versionError) {
              console.error('ComponentUpdater: Error fetching versions:', versionError)
              return
            }

            const nextVersionNumber = (versions?.[0]?.version_number || 0) + 1

            const { error: createVersionError } = await supabase
              .from('document_versions')
              .insert({
                document_id: documentId,
                version_number: nextVersionNumber,
                title: `Version ${nextVersionNumber}`,
                content,
                data,
                created_by: stateRef.current.editor.storage?.user?.id,
                change_summary: changeSummary,
                is_auto_save: false,
              })

            if (createVersionError) {
              console.error('ComponentUpdater: Error creating version:', createVersionError)
              return
            }

            console.log(`ComponentUpdater: Created version ${nextVersionNumber} with summary: ${changeSummary}`)
          } catch (error) {
            console.error('ComponentUpdater: Unexpected error creating version:', error)
          }
        }
      }, 100)
    },
    [stateRef, documentId]
  )

  const handleSectionStartLoading = useCallback(
    (id: string, existing: ReportComponent) => {
      console.log(`ComponentUpdater: Section ${id} started loading, notifying dependent components`)

      setTimeout(() => {
        // Find parent groups and update them from error to loading
        const parentGroups = Array.from(stateRef.current.components.values()).filter(
          (comp) => comp.type === 'report-group' && (
            comp.children?.includes(id) ||
            existing?.parentId === comp.id ||
            id.startsWith(comp.id + '-')
          )
        )

        parentGroups.forEach((group) => {
          if (group.status === 'error' && !updatingComponentsRef.current.has(group.id)) {
            console.log(`ComponentUpdater: Updating parent group ${group.id} from error to loading`)
            dispatch({
              type: 'COMPONENT_UPDATED',
              id: group.id,
              updates: {
                status: 'loading',
                error: undefined
              }
            })
          }
        })

        // Find dependent summaries and update them from error to loading
        const dependentSummaries = Array.from(stateRef.current.components.values()).filter(
          (comp) => comp.type === 'report-summary' && (
            comp.dependencies?.includes(id) ||
            comp.dependencies?.includes(existing?.parentId || '') ||
            (existing?.parentId && comp.dependencies?.includes(existing.parentId))
          )
        )

        dependentSummaries.forEach((summary) => {
          if (summary.status === 'error' && !updatingComponentsRef.current.has(summary.id)) {
            console.log(`ComponentUpdater: Updating dependent summary ${summary.id} from error to loading`)
            dispatch({
              type: 'COMPONENT_UPDATED',
              id: summary.id,
              updates: {
                status: 'loading',
                error: undefined
              }
            })
          }
        })
      }, 0)
    },
    [stateRef, dispatch, updatingComponentsRef]
  )

  const handleDependencyUpdates = useCallback(
    (id: string) => {
      setTimeout(() => {
        Array.from(stateRef.current.components.values())
          .filter(
            (comp) =>
              comp.type === 'report-summary' && comp.dependencies?.includes(id)
          )
          .forEach((summary) => {
            if ((summary.status === 'idle' || summary.status === 'loading') &&
                !updatingComponentsRef.current.has(summary.id)) {
              console.log(`ComponentUpdater: Triggering re-evaluation for summary ${summary.id}`)
              dispatch({
                type: 'COMPONENT_UPDATED',
                id: summary.id,
                updates: { lastRefreshed: new Date() }
              })
            }
          })
      }, 0)
    },
    [stateRef, dispatch, updatingComponentsRef]
  )

  const handleParentGroupUpdates = useCallback(
    (id: string, existing: ReportComponent) => {
      // Find parent groups and trigger their status evaluation
      if (existing?.parentId) {
        console.log(`ComponentUpdater: Component ${id} has parent ${existing.parentId}, triggering parent group evaluation`)
        updateGroupStatus(existing.parentId, false)
      }

      // Also trigger group status updates for any groups that contain this component
      const containingGroups = Array.from(stateRef.current.components.values()).filter(
        (comp) => comp.type === 'report-group' && comp.children?.includes(id)
      )

      containingGroups.forEach((group) => {
        console.log(`ComponentUpdater: Component ${id} is contained in group ${group.id}, triggering group evaluation`)
        updateGroupStatus(group.id, false)
      })
    },
    [stateRef, updateGroupStatus]
  )

  const triggerDocumentSave = useCallback(
    () => {
      setTimeout(() => {
        if (stateRef.current.editor) {
          dispatch({
            type: 'CONTENT_CHANGED',
            content: stateRef.current.editor.getHTML(),
            data: stateRef.current.editor.getJSON(),
            source: 'system',
          })
        }
      }, 100)
    },
    [stateRef, dispatch]
  )

  return {
    updateComponent
  }
}