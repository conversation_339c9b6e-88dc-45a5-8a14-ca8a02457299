# Customer App Editor Component

## Table of Contents

1. [Overview](#overview)
2. [Architecture Overview](#architecture-overview)
3. [Key Features](#key-features)
4. [Extension System](#extension-system)
5. [State Management](#state-management)
6. [Services Architecture](#services-architecture)
7. [Hooks](#hooks)
8. [AI Integration](#ai-integration)
9. [Collaboration Features](#collaboration-features)
10. [UI Components](#ui-components)
11. [Performance Optimizations](#performance-optimizations)
12. [Testing](#testing)
13. [Quick Start](#quick-start)
14. [Common Patterns](#common-patterns)
15. [Troubleshooting](#troubleshooting)
16. [Future Enhancements](#future-enhancements)

## Overview

The Editor component is the core text editing functionality in the Customer App. It's a sophisticated, feature-rich editor built on top of TipTap (which uses ProseMirror) with extensive customizations for document creation, collaboration, and AI-powered features.

### Technology Stack

The editor leverages a carefully chosen technology stack optimized for performance, maintainability, and user experience:

#### **TipTap & ProseMirror** - Rich Text Editor Core
- **TipTap** (`@tiptap/core: ^2.12.0`) - Modern headless editor framework providing a clean abstraction over ProseMirror
- **ProseMirror** (`prosemirror-state: ^1.4.3`) - Battle-tested editor engine with powerful document model
- **Why chosen**: Modular architecture with 18+ custom extensions, seamless React integration, built-in collaborative editing support, and strong TypeScript compatibility
- **Key features**: Extension system, real-time collaboration via YJS, custom node views, and command system

#### **Supabase** - Backend & Real-time Infrastructure
- **Client SDK** (`@supabase/supabase-js: latest`) - JavaScript client for database operations
- **Real-time** (`y-websocket: ^3.0.0`, `yjs: ^13.6.27`) - Conflict-free collaborative editing
- **Why chosen**: Built-in WebSocket support for real-time collaboration, PostgreSQL backend with automatic versioning, integrated authentication, and scalable architecture
- **Key features**: Auto-save functionality, document versioning, user presence tracking, and collaborative sessions

#### **React & TypeScript** - Component Framework
- **React** (`^18.0.0`) - Component-based UI development with hooks
- **TypeScript** (`strict: true`) - Type-safe development with full DOM API support
- **Why chosen**: Type safety prevents runtime errors in complex editor state, enhanced developer experience with IntelliSense, safe refactoring across large codebase
- **Key features**: Path mapping (`@/*` aliases), incremental compilation, custom context patterns for state management

#### **Tailwind CSS with Custom Design System** - Styling Architecture
- **Core** (`tailwindcss: ^3.4.0`) - Utility-first CSS framework
- **Custom Plugins** - Modular plugins for glass-morphism effects and brand gradients
- **Why chosen**: Performance through tree-shaking, design system enforcement, extensive customization capabilities, maintainable co-located styles
- **Key features**: Glass-morphism effects, responsive design, print media support, consistent design tokens

#### **Radix UI** - Accessible Component Primitives
- **Components** (`@radix-ui/react-*`) - Dropdown menus, dialogs, tooltips, form controls
- **Why chosen**: WCAG-compliant accessibility out of the box, unstyled primitives allowing full design control, complete keyboard navigation support
- **Key features**: Composable component patterns, aria attributes, focus management, keyboard shortcuts

#### **AI Integration Stack** - Multi-Provider AI Support
- **Anthropic** (`@ai-sdk/anthropic: ^1.2.11`) - Claude integration for text analysis and generation
- **OpenAI** (`openai: ^4.100.0`) - GPT support for alternative AI responses
- **Google** (`@ai-sdk/google: ^1.2.18`) - Gemini integration for diverse AI capabilities
- **Why chosen**: Unified interface across providers, streaming responses for real-time feedback, context-aware prompting based on document structure

## Architecture Overview

The editor recently underwent a major refactoring (January 2025) to improve performance, maintainability, and developer experience. The architecture was transformed from a monolithic 515-line context into a modular, service-oriented design.

### Design Philosophy

The editor architecture follows five core principles that guide all development decisions:

#### 1. **Separation of Concerns** - Single Responsibility Principle
Each module has a single, well-defined responsibility with clear boundaries:

- **DocumentContext**: Only handles document state, loading, and saving operations
- **ComponentContext**: Exclusively manages component registration, status tracking, and lifecycle
- **VersioningContext**: Solely responsible for version history and auto-save functionality
- **DependencyContext**: Purely handles component dependency resolution and load ordering

**Example**: Before refactoring, DocumentContext handled 515 lines mixing document operations, component management, versioning, and performance monitoring. After refactoring, these concerns are cleanly separated into focused contexts of ~150 lines each.

#### 2. **Service Layer Pattern** - Business Logic Separation
All business logic resides in dedicated service classes, completely separated from UI components:

**Service Examples**:
```typescript
// ReportService - Document operations
await reportService.createReport({
  title: 'New Report',
  content: initialContent,
  entityId: 'ENT-123'
})

// VersionService - Version management  
await versionService.createVersion({
  documentId: 'doc-456',
  content: editorHTML,
  isAutoSave: true
})
```

**Why this matters**: UI components become pure presentation logic, making them easier to test, maintain, and reuse. Business rules are centralized and consistent across the application.

#### 3. **Performance First** - Optimization Built Into Architecture
Performance optimizations are architectural decisions, not afterthoughts:

**Debounced Updates**: Group status calculations are debounced at 50ms to prevent UI blocking during rapid changes
```typescript
const debouncedUpdateGroupStatus = debounce((groupId: string) => {
  groupStatusManager.calculateGroupStatus(groupId)
}, 50)
```

**Memoized Contexts**: All context values are properly memoized to prevent unnecessary re-renders
```typescript
const contextValue = useMemo(() => ({
  components: componentsMap,
  registerComponent,
  updateComponentStatus
}), [componentsMap, registerComponent, updateComponentStatus])
```

**Result**: 50% reduction in unnecessary re-renders compared to the previous monolithic architecture.

#### 4. **Developer Experience** - Type-Safe, Testable, Debuggable
Every aspect prioritizes developer productivity and code quality:

**Type Safety**: Comprehensive TypeScript coverage with strict mode enabled
```typescript
interface ComponentRegistration {
  id: string
  type: ComponentType
  status: ComponentStatus
  parentId?: string
  dependencies?: string[]
}
```

**Testability**: Service layer enables comprehensive unit testing
```typescript
describe('ComponentStateMachine', () => {
  it('validates valid status transitions', () => {
    expect(stateMachine.canTransition('registering', 'waiting')).toBe(true)
    expect(stateMachine.canTransition('loaded', 'registering')).toBe(false)
  })
})
```

**Debuggability**: Centralized logging with component-scoped context
```typescript
logger.info('Component registered', { 
  componentId: 'comp-123', 
  type: 'report-section',
  context: 'ComponentContext' 
})
```

#### 5. **Backward Compatibility** - Gradual Migration Path
The refactoring maintains complete backward compatibility while enabling gradual migration:

**Legacy API Support**: Old hooks continue to work during transition period
```typescript
// Legacy hook (still works)
const { isLoading, error } = useDocumentContext()

// New hooks (preferred)
const { isLoading } = useDocument()
const { error } = useDocumentErrors()
```

**Migration Strategy**: Components can be migrated one by one without breaking existing functionality, reducing risk and enabling incremental improvements.

**Benefits**: Teams can adopt new patterns at their own pace while immediately benefiting from performance improvements and bug fixes in the new architecture.

### Core Components

The editor system is built around three primary components, each with distinct responsibilities and use cases:

#### 1. **EkoDocumentEditor.tsx** - Main Editor Orchestrator

**Purpose**: The primary component that coordinates all editor functionality, bringing together rich text editing, AI features, collaboration tools, and document operations in a cohesive interface.

**Key Responsibilities**:
- Manages complete TipTap editor instance with 18+ custom extensions
- Handles document initialization, content processing, and state management
- Provides error boundaries and safe wrappers around editor content
- Coordinates between AI features, collaboration tools, and document operations

**Configuration Options**:
```typescript
interface EkoDocumentEditorProps {
  documentId: string                 // Required document identifier
  citations?: CitationType[]         // Document citations for inline references
  initialContent?: string           // Markdown or HTML content to load
  initialData?: any                 // Structured JSON data for TipTap
  onSave?: (content: string, data?: any) => Promise<void>
  editable?: boolean               // Enable/disable editing (default: true)
  showToolbar?: boolean            // Show editor toolbar (default: true)
  showCollaboration?: boolean      // Enable collaboration features (default: true)
  showAI?: boolean                 // Enable AI features (default: true)
  viewMode?: boolean               // Read-only presentation mode
  printMode?: boolean              // Print-optimized styling
  entityId?: string | null         // Entity association for reports
  runId?: string                   // Run ID for report context
  user?: UserObject                // Current user information
  featureFlags?: FeatureFlags      // Granular feature control
}
```

**Main Features**:
- **Rich Text Editing**: Full TipTap/ProseMirror integration with custom extensions
- **Auto-save**: Supabase-based auto-save with configurable intervals (default: 5s)
- **AI Integration**: Custom AI provider with streaming responses and change tracking
- **Collaboration**: Real-time editing, presence indicators, comments, version history
- **Content Processing**: Markdown-to-HTML conversion with citation processing
- **Accessibility**: ARIA support, screen reader announcements, keyboard navigation

**Usage Examples**:
```typescript
// Basic setup
<EkoDocumentEditor
  documentId="doc-123"
  initialContent="# My Document"
  onSave={handleSave}
/>

// Advanced configuration for reports
<EkoDocumentEditor
  documentId={documentId}
  citations={citations}
  entityId="ENT-456"
  runId="RUN-789"
  user={currentUser}
  featureFlags={{ aiTools: true, comments: true, exportWord: true }}
  onSave={handleSave}
/>
```

#### 2. **PublicDocumentViewer.tsx** - Read-Only Document Display

**Purpose**: Provides a clean, optimized interface for viewing shared documents without editing capabilities, designed for public consumption and presentation.

**Key Responsibilities**:
- Renders documents in read-only mode with full formatting preserved
- Provides print functionality with optimized print styles
- Maintains accessibility features while removing interactive elements
- Supports citation display without edit capabilities

**Configuration Options**:
```typescript
interface PublicDocumentViewerProps {
  documentId?: string              // Optional document identifier
  title?: string                   // Document title for header
  content?: string                 // Raw content (markdown/HTML)
  data?: any                      // Structured TipTap JSON data
  citations?: any[]               // Citation references
  className?: string              // Custom styling
}
```

**Main Features**:
- **Public Header**: Clean header with document title and "read-only" indicator
- **Print Support**: Built-in print button with optimized print styles
- **Citation Rendering**: Full citation display without edit capabilities
- **Responsive Design**: Mobile-friendly layout with proper typography
- **Performance Optimized**: Lighter weight than full editor for faster loading

**Usage Examples**:
```typescript
// Simple public viewing
<PublicDocumentViewer
  title="Shared Report"
  content={markdownContent}
  citations={citationData}
/>

// With structured TipTap data
<PublicDocumentViewer
  documentId="public-doc-123"
  data={tiptapJsonData}
  citations={citations}
/>
```

#### 3. **DocumentList.tsx** - Document Management Interface

**Purpose**: Comprehensive document listing and management interface that handles document CRUD operations, real-time updates, and access control for both owned and shared documents.

**Key Responsibilities**:
- Manages document CRUD operations through Supabase integration
- Provides real-time document updates via Supabase subscriptions
- Handles search, filtering, and document organization
- Manages access control for owned vs. shared documents

**Configuration Options**:
```typescript
interface DocumentListProps {
  currentUser?: UserObject         // Current user for ownership checks
  onSelectDocument?: (documentId: string) => void  // Document selection handler
  onCreateDocument?: () => void    // Custom document creation
  selectedDocumentId?: string     // Currently selected document
  className?: string              // Custom styling
}
```

**Main Features**:
- **Real-time Updates**: Live document list updates via Supabase subscriptions
- **Search and Filter**: Full-text search and filtering by ownership (My/Shared)
- **Document Metadata**: Author information, creation/update timestamps, ownership indicators
- **Access Control**: Visual differentiation between owned and shared documents
- **Mobile Responsive**: Sidebar layout with proper scrolling and touch support

**Usage Examples**:
```typescript
// Basic document management
<DocumentList
  currentUser={user}
  onSelectDocument={handleSelectDocument}
  selectedDocumentId={currentDocumentId}
/>

// With custom creation workflow
<DocumentList
  currentUser={user}
  onSelectDocument={handleSelectDocument}
  onCreateDocument={handleCustomCreation}
/>
```

#### Component Integration Architecture

All three components integrate through a shared architecture:

**Shared Context System**:
- **DocumentContext**: Document state, component tracking, versioning
- **ComponentContext**: Report sections, groups, and summaries management
- **DependencyContext**: Component dependency resolution and load ordering

**Common Extension System**:
- Extensions adapt behavior based on component type (editable vs. read-only)
- Shared citation processing and content rendering pipeline
- Consistent styling through common CSS classes and Tailwind utilities

**Performance Optimizations**:
- Proper memoization prevents unnecessary re-renders
- Debounced operations for group status updates and auto-save
- Automatic resource cleanup for subscriptions and timeouts
- Error boundaries prevent component crashes from affecting the application

### Directory Structure

The editor component follows a well-structured modular architecture that separates concerns into distinct functional areas. At the root level, the component serves as the entry point through `index.ts`, which exports the main EditorProvider along with individual context providers for advanced usage scenarios. This design allows developers to use either the complete editor system or selectively import specific functionality as needed.

The **context directory** forms the backbone of the editor's state management system, implementing a sophisticated hierarchy of specialized contexts. The main DocumentContext sits alongside specialized subdirectories for different aspects of the editor. The component subdirectory manages the registration and lifecycle of individual report components, while the dependency subdirectory handles inter-component dependencies. The document subdirectory focuses specifically on document-level operations, and the versioning subdirectory manages document version control. Within the hooks subdirectory, specialized hooks like `useComponentRegistration`, `useComponentUpdater`, and `useDependencyManager` provide the actual implementation logic for these contexts. The providers subdirectory contains the main EditorProvider that orchestrates all these contexts together.

The **extensions directory** represents the editor's plugin architecture, containing a rich collection of TipTap extensions that provide the editor's functionality. This includes AI-powered features through `AICommandExtension` and `AISlashCommandExtension`, content structuring through `ReportSectionExtension` and `TableOfContentsExtension`, and specialized document features like `CitationExtension` and `MathematicsExtension`. The extensions also include accessibility support and collaborative features through `ChangeTrackingExtension`. The presence of both newer extensions and legacy extensions suggests an ongoing evolution of the plugin system.

The **services directory** contains the core business logic and utilities that power the editor. The state subdirectory houses `componentStateMachine.ts`, which implements a finite state machine for managing component lifecycles and transitions. The supabase subdirectory contains integration services that handle database operations and version control. The utils subdirectory provides essential utilities including comprehensive logging, performance monitoring, memory management, and operational utilities for group management and timeout handling. These services work together to provide a robust foundation for the editor's operations.

The **components directory** contains smaller, reusable UI components that enhance the editor experience. These include interactive elements like context bubble menus and right-click context menus, as well as specialized components for AI slash commands and cursor indicators that provide visual feedback and user interaction capabilities.

The **hooks directory** provides custom React hooks that encapsulate complex editor behaviors. These include general editor operations, automatic document saving functionality, and report management capabilities. The hooks serve as the primary interface between React components and the underlying editor services.

The **panels directory** contains larger UI components that provide extended functionality through side panels. These include AI interaction panels, collaborative commenting interfaces, version history browsers, and document sharing controls. The tabbed side panel serves as a container that organizes these various panels into a cohesive interface.

The **types directory** contains TypeScript type definitions that provide type safety across the entire editor system. The main exports include interfaces for core concepts like report components, component updates, document state, and various status enums that maintain consistency across the application.

The **utils directory** provides utility functions for specific operations like markdown processing, export functionality, and schema serialization. These utilities handle the more technical aspects of document manipulation and format conversion.

The **templates directory** contains document templating functionality, allowing users to create new documents from predefined structures. This includes both static templates and dynamic template generation capabilities.

The **toolbar directory** houses various toolbar components including basic editing functions, AI-powered features, and collaboration toolbars for real-time editing capabilities.

The **dialogs directory** provides modal interfaces for specific operations like collaboration settings and report component configuration.

This architecture demonstrates a sophisticated approach to building a collaborative document editor that separates concerns effectively while maintaining strong integration between components. The extensive use of context providers, hooks, and services creates a flexible system that can accommodate complex document editing scenarios while remaining maintainable and extensible.

## Key Features

### 1. Rich Text Editing
- Built on TipTap/ProseMirror for robust editing capabilities
- Custom extensions for specialized functionality

### 2. AI Integration
- AI-powered slash commands
- AI chat panel for assistance
- Custom AI provider integration

### 3. Collaboration Features
- Real-time collaboration support
- Presence indicators
- Collaboration settings management

### 4. Document Management
- Auto-save functionality
- Version control
- Document templates
- Export utilities

### 5. Report Generation
- Report sections and groups
- Report summaries
- Dynamic component system

## Extension System

The editor leverages TipTap's extension architecture to provide rich functionality through modular components. Each extension adds specific capabilities to the editor.

### Core Extensions

#### Content Extensions
- **CitationExtension** - Handles inline citations with badges and tooltips
- **ReferencesExtension** - Manages footnotes and reference lists at document end
- **TableOfContentsExtension** - Auto-generates and maintains document outline
- **MathematicsExtension** - LaTeX math rendering using KaTeX
- **ChartExtension** - Interactive chart embedding
- **DetailsExtension** - Collapsible content sections

#### Report Extensions
- **ReportSectionExtension** - Major report sections with status tracking
- **ReportGroupComponent** - Grouping of related components
- **ReportSummaryExtension** - Executive summaries with special styling

#### Interactive Extensions
- **SlashCommands** - Command palette triggered by "/" for quick actions
- **AISlashCommandExtension** - AI-powered commands for text manipulation
- **AICommandExtension** - Context-aware AI assistance
- **EmojiExtension** - Emoji picker and insertion
- **FileHandlerExtension** - Drag-and-drop file uploads to Supabase

#### UI/UX Extensions
- **ContextMenuExtension** - Right-click context menus
- **ColumnsExtension** - Multi-column layouts
- **UniqueIdExtension** - Automatic unique IDs for headings/paragraphs
- **AccessibilityExtension** - ARIA roles and keyboard navigation
- **ChangeTrackingExtension** - Track edits with user attribution

### Extension Architecture

Extensions follow TipTap's pattern:
```typescript
export const MyExtension = Extension.create({
  name: 'myExtension',
  
  addOptions() {
    return {
      // Configuration options
    }
  },
  
  addCommands() {
    return {
      // Editor commands
    }
  },
  
  addKeyboardShortcuts() {
    return {
      // Keyboard bindings
    }
  },
  
  // Additional methods...
})

```

## State Management

The editor implements a sophisticated state management system using React Context API, following the architecture patterns documented in `CLAUDE.md`.

### Context Architecture

The editor uses four specialized contexts, each with a single responsibility:

#### 1. DocumentContext
- **Purpose**: Manages document state and operations
- **Features**:
  - Document loading and saving
  - Editor instance management
  - Entity and run ID tracking
  - Document status tracking
- **Location**: `context/document/DocumentContext.tsx`

#### 2. ComponentContext  
- **Purpose**: Handles component registration and lifecycle
- **Features**:
  - Component registration/unregistration
  - Status tracking with state machine validation
  - Hierarchy management (parent-child relationships)
  - Component metadata storage
- **Location**: `context/component/ComponentContext.tsx`

#### 3. VersioningContext
- **Purpose**: Version control and history management
- **Features**:
  - Version creation and restoration
  - Auto-save version management
  - Version history browsing
  - Diff visualization preparation
- **Location**: `context/versioning/VersioningContext.tsx`

#### 4. DependencyContext
- **Purpose**: Component dependency resolution
- **Features**:
  - Dependency registration
  - Circular dependency detection
  - Load order optimization
  - Dependency status tracking
- **Location**: `context/dependency/DependencyContext.tsx`

### Provider Hierarchy

All contexts are composed through the `EditorProvider`:

```typescript
<EditorProvider reportId={id} onSave={handleSave}>
  <DocumentProvider>
    <ComponentProvider>
      <DependencyProvider>
        <VersioningProvider>
          {/* Editor content */}
        </VersioningProvider>
      </DependencyProvider>
    </ComponentProvider>
  </DocumentProvider>
</EditorProvider>
```

### State Machine

The editor uses a state machine to ensure valid component status transitions:

```typescript
// Valid transitions:
unregistered -> registering -> waiting/loading -> loaded/error
loaded -> refreshing -> loaded/error
// And more...
```

## Services Architecture

The editor follows a clean service layer pattern, separating business logic from UI components.

### Service Layer Structure

```
services/
├── supabase/              # Database operations
│   ├── reportService.ts   # Document CRUD
│   └── versionService.ts  # Version management
├── state/                 # State management
│   └── componentStateMachine.ts
└── utils/                 # Shared utilities
    ├── logger.ts          # Centralized logging
    ├── timeouts.ts        # Timeout management
    ├── constants.ts       # Status constants
    ├── groupManager.ts    # Group status calculations
    ├── memoryManager.ts   # Resource cleanup
    └── performanceMonitor.ts
```

### Key Services

#### ReportService
- **Purpose**: Document persistence operations
- **Methods**:
  - `createReport()` - Create new documents
  - `getReport()` - Retrieve documents
  - `updateReport()` - Save changes
  - `deleteReport()` - Remove documents
- **Features**: Automatic retry, error handling, validation

#### VersionService
- **Purpose**: Version control operations
- **Methods**:
  - `createVersion()` - Save document snapshot
  - `getVersions()` - List version history
  - `restoreVersion()` - Revert to previous version
  - `cleanupAutoSaves()` - Manage auto-save versions
- **Features**: Auto-save management, diff preparation

#### State Machine Service
- **Purpose**: Validate component status transitions
- **Features**:
  - Prevents invalid state changes
  - Enforces business rules
  - Provides transition validation
  - Supports dependency-aware transitions

#### Utility Services

**Logger**
- Centralized logging with levels (debug, info, warn, error)
- Component-scoped logging
- Performance tracking integration

**TimeoutRegistry**
- Manages all timeouts/intervals
- Automatic cleanup on unmount
- Configurable delays
- Debounce/throttle utilities

**MemoryManager**
- Tracks resource allocation
- Scoped memory management
- Automatic cleanup
- Memory leak detection

**PerformanceMonitor**
- Method execution timing
- Render performance tracking
- Issue detection and reporting
- Decorator-based instrumentation

**GroupStatusManager**
- Calculates aggregate group status
- Manages status update queuing
- Implements debounced updates
- Caches calculations

## Hooks

The editor provides several custom hooks for common functionality:

### Document Management Hooks

#### useSupabaseAutoSave
- **Purpose**: Handles automatic saving to Supabase
- **Features**:
  - Configurable save intervals (default: 5s)
  - Version creation at intervals
  - Manual save capability
  - Unsaved changes tracking
  - Error handling and retry
- **Usage**:
```typescript
const { isSaving, lastSaved, hasUnsavedChanges, manualSave } = useSupabaseAutoSave({
  documentId,
  editor,
  onSave: handleSave,
  saveInterval: 5000
})
```

#### useDocumentAutoSave
- Legacy auto-save hook (being phased out)
- Replaced by `useSupabaseAutoSave`

### Collaboration Hooks

#### usePresence
- **Purpose**: Real-time user presence tracking
- **Features**:
  - Shows active users in document
  - Cursor position tracking
  - Selection synchronization
  - Online/offline status
- **Usage**:
```typescript
const { activeUsers, isConnected } = usePresence({
  documentId,
  user: currentUser,
  editor
})
```

### Report Management Hooks

#### useReportManager
- **Purpose**: Manages report components and hierarchy
- **Features**:
  - Component registration
  - Status tracking
  - Dependency resolution
  - Group management

### Editor Hook

#### useEditor
- Core TipTap hook for editor instance
- Extended with custom functionality

## AI Integration

The editor includes sophisticated AI capabilities through the `CustomAIProvider`:

### AI Features

1. **AI Commands**
   - Text improvement
   - Grammar correction
   - Content expansion/compression
   - Tone adjustment
   - Summarization

2. **AI Chat Panel**
   - Interactive AI assistant
   - Context-aware suggestions
   - Document-wide edits
   - Change tracking for AI edits

3. **AI Slash Commands**
   - Quick AI actions via "/"
   - Inline text transformations
   - Smart completions

### AI Architecture

```typescript
const aiProvider = new CustomAIProvider({
  apiKey: 'your-api-key',
  baseUrl: '/api/ai'
})
```

The AI system uses:
- Streaming responses for real-time feedback
- Change tracking to show AI modifications
- Context preservation for coherent edits
- Error handling and fallbacks

## Collaboration Features

### Real-time Collaboration

The editor supports real-time collaboration through Supabase:

1. **Presence System**
   - Active user display
   - Cursor indicators
   - Selection highlights
   - User avatars and colors

2. **Comments**
   - Inline comments
   - Thread discussions
   - Mention system
   - Resolution tracking

3. **Version History**
   - Browse previous versions
   - Restore to any version
   - Diff visualization
   - Auto-save versions

4. **Sharing**
   - Permission management
   - Public/private documents
   - Collaboration settings
   - Access control

## UI Components

### Toolbars

1. **EditorToolbar**
   - Text formatting controls
   - Block type selection
   - Table and list tools
   - Export options

2. **SupabaseCollaborationToolbar**
   - Active users display
   - Comments toggle
   - History access
   - Share controls

3. **AIToolbar**
   - AI command buttons
   - Chat panel toggle
   - AI status indicator

### Panels

1. **TabbedSidePanel**
   - Comments view
   - History browser
   - Share settings
   - AI chat

2. **Context Menus**
   - Right-click menu
   - Bubble menu for selections
   - Quick actions

### Dialogs

1. **ReportComponentDialog**
   - Add report sections
   - Configure components
   - Set dependencies

2. **CollaborationSettingsDialog**
   - Permission management
   - Access control
   - Collaboration preferences

## Performance Optimizations

The editor implements several performance optimizations:

1. **Debounced Updates**
   - Group status calculations debounced at 50ms
   - Auto-save debounced at 5s
   - Presence updates throttled

2. **Memoization**
   - Context values memoized
   - Extension configuration cached
   - Component calculations cached

3. **Resource Management**
   - Automatic cleanup via MemoryManager
   - Scoped resource tracking
   - Timeout registry

4. **Lazy Loading**
   - Extensions loaded on demand
   - Panels rendered when opened
   - Heavy operations deferred

## Testing

The editor includes comprehensive testing:

1. **Unit Tests**
   - Utility functions
   - State machines
   - Service layer

2. **Integration Tests**
   - Component lifecycle
   - Context interactions
   - Extension behavior

3. **E2E Tests**
   - User workflows
   - Collaboration scenarios
   - AI interactions

## Quick Start

### Basic Usage

```typescript
import { EkoDocumentEditor } from '@/components/editor'

function MyDocument() {
  const handleSave = async (content: string, data: any) => {
    // Save logic here
  }

  return (
    <EkoDocumentEditor
      documentId="doc-123"
      initialContent="# Welcome to the editor"
      onSave={handleSave}
      showToolbar={true}
      showCollaboration={true}
      showAI={true}
    />
  )
}
```

### With Custom Configuration

```typescript
<EkoDocumentEditor
  documentId={documentId}
  citations={citations}
  initialContent={markdown}
  editable={true}
  viewMode={false}
  entityId={entityId}
  runId={runId}
  user={{
    id: user.id,
    name: user.name,
    email: user.email,
    avatar: user.avatar_url
  }}
  featureFlags={{
    aiTools: true,
    aiChat: true,
    comments: true,
    share: true,
    exportWord: true
  }}
  onSave={handleSave}
/>
```

## Common Patterns

### Adding a New Extension

```typescript
export const MyExtension = Extension.create({
  name: 'myExtension',
  
  addOptions() {
    return {
      // Default options
    }
  },
  
  addCommands() {
    return {
      myCommand: () => ({ commands }) => {
        // Command implementation
        return commands.insertContent('Hello')
      }
    }
  }
})
```

### Using the Service Layer

```typescript
import { reportService } from './services'

// Create document
const report = await reportService.createReport({
  title: 'New Report',
  content: initialContent
})

// Update document
await reportService.updateReport(reportId, {
  content: newContent,
  data: editorData
})
```

### Managing Component State

```typescript
const { registerComponent, updateComponentStatus } = useComponents()

// Register component
registerComponent({
  id: 'comp-1',
  type: 'report-section',
  status: 'registering'
})

// Update status
updateComponentStatus('comp-1', 'loaded')
```

## Troubleshooting

### Common Issues

1. **Editor not loading**
   - Check if `initialContent` is valid
   - Verify Supabase connection
   - Check browser console for errors

2. **Auto-save not working**
   - Verify user authentication
   - Check document permissions
   - Ensure valid document ID

3. **Extensions not appearing**
   - Check extension registration
   - Verify TipTap compatibility
   - Review extension dependencies

4. **Performance issues**
   - Enable performance monitoring
   - Check for render loops
   - Review memory usage

### Debug Mode

Enable debug logging:
```typescript
import { logger } from './services'
logger.setLevel('debug')
```

## Future Enhancements

Planned improvements include:

1. **Enhanced AI Features**
   - More AI commands
   - Better context understanding
   - Multi-language support

2. **Advanced Collaboration**
   - Voice/video integration
   - Real-time cursors
   - Conflict resolution

3. **Performance**
   - Virtual scrolling
   - Web Workers for heavy operations
   - Better caching strategies

4. **Accessibility**
   - Screen reader improvements
   - Keyboard navigation enhancements
   - WCAG compliance

---

*Last updated: January 2025*