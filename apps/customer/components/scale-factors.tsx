import React from 'react'
import { ScaleFactors } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { Progress } from '@ui/components/ui/progress'
import { 
  Target, 
  AlertTriangle, 
  Zap, 
  Shield, 
  Brain, 
  Percent, 
  Clock, 
  RotateCcw, 
  Globe 
} from 'lucide-react'
import { cn } from '@ui/lib/utils'

interface ScaleFactorsProps {
  scaleFactors: ScaleFactors
  className?: string
}

const getDurationColor = (duration: 'short' | 'medium' | 'long') => {
  switch (duration) {
    case 'short': return 'bg-green-500'
    case 'medium': return 'bg-yellow-500'
    case 'long': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getDurationLabel = (duration: 'short' | 'medium' | 'long') => {
  switch (duration) {
    case 'short': return 'Short-term'
    case 'medium': return 'Medium-term'
    case 'long': return 'Long-term'
    default: return 'Unknown'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 80) return 'text-red-600'
  if (score >= 60) return 'text-orange-500'
  if (score >= 40) return 'text-yellow-500'
  if (score >= 20) return 'text-blue-500'
  return 'text-green-600'
}

const getProgressColor = (score: number) => {
  if (score >= 80) return 'bg-red-500'
  if (score >= 60) return 'bg-orange-500'
  if (score >= 40) return 'bg-yellow-500'
  if (score >= 20) return 'bg-blue-500'
  return 'bg-green-500'
}

interface ScaleFactorItemProps {
  icon: React.ReactNode
  label: string
  value: number | string
  description: string
  showProgress?: boolean
  progressColor?: string
  valueColor?: string
}

function ScaleFactorItem({ 
  icon, 
  label, 
  value, 
  description, 
  showProgress = false,
  progressColor,
  valueColor
}: ScaleFactorItemProps) {
  const numericValue = typeof value === 'number' ? value : 0
  const displayValue = typeof value === 'string' ? value : `${value}%`
  
  return (
    <div className="flex items-start gap-3 p-3 rounded-lg bg-slate-50/50 dark:bg-slate-800/50 backdrop-blur-sm">
      <div className="flex-shrink-0 p-2 rounded-full bg-white/80 dark:bg-slate-700/80 shadow-sm">
        {icon}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <h4 className="text-sm font-medium text-slate-900 dark:text-slate-100">
            {label}
          </h4>
          <span className={cn("text-sm font-semibold", valueColor)}>
            {displayValue}
          </span>
        </div>
        {showProgress && typeof value === 'number' && (
          <div className="mb-2">
            <Progress 
              value={numericValue} 
              className="h-2" 
              style={{ 
                '--progress-background': progressColor 
              } as React.CSSProperties}
            />
          </div>
        )}
        <p className="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
          {description}
        </p>
      </div>
    </div>
  )
}

export function ScaleFactorsDisplay({ scaleFactors, className }: ScaleFactorsProps) {
  return (
    <Card className={cn("glass-card", className)}>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Target className="w-5 h-5" />
          Scale Factors
          <Badge variant="outline" className="ml-auto">
            Impact Assessment
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-3">
          {/* Scale of Impact */}
          <ScaleFactorItem
            icon={<Globe className="w-4 h-4 text-blue-600" />}
            label="Scale of Impact"
            value={scaleFactors.scale_of_impact.toLocaleString()}
            description="Number of living beings impacted or hectares affected"
          />

          {/* Damage */}
          <ScaleFactorItem
            icon={<AlertTriangle className="w-4 h-4 text-red-600" />}
            label="Damage Level"
            value={scaleFactors.damage}
            description="Degree of damage caused (100=total destruction, 60=severe, 30=moderate, 10=minor, 0=none)"
            showProgress={true}
            progressColor={getProgressColor(scaleFactors.damage)}
            valueColor={getScoreColor(scaleFactors.damage)}
          />
          
          {/* Proximity to Tipping Point */}
          <ScaleFactorItem
            icon={<AlertTriangle className="w-4 h-4 text-orange-600" />}
            label="Tipping Point Proximity"
            value={scaleFactors.proximity_to_tipping_point}
            description="How close the impact brings us to critical environmental or social thresholds"
          />
          
          {/* Directness */}
          <ScaleFactorItem
            icon={<Target className="w-4 h-4 text-purple-600" />}
            label="Directness"
            value={scaleFactors.directness}
            description="How directly the entity is responsible for the impact"
            showProgress={true}
            progressColor={getProgressColor(scaleFactors.directness)}
            valueColor={getScoreColor(scaleFactors.directness)}
          />
          
          {/* Authenticity */}
          <ScaleFactorItem
            icon={<Shield className="w-4 h-4 text-green-600" />}
            label="Authenticity"
            value={scaleFactors.authenticity}
            description="Whether the action is genuine or driven by compliance/marketing"
            showProgress={true}
            progressColor={getProgressColor(scaleFactors.authenticity)}
            valueColor={getScoreColor(scaleFactors.authenticity)}
          />
          
          {/* Deliberateness */}
          <ScaleFactorItem
            icon={<Brain className="w-4 h-4 text-indigo-600" />}
            label="Deliberateness"
            value={scaleFactors.deliberateness}
            description="Whether the impact was intentional or accidental"
            showProgress={true}
            progressColor={getProgressColor(scaleFactors.deliberateness)}
            valueColor={getScoreColor(scaleFactors.deliberateness)}
          />
          
          {/* Contribution */}
          <ScaleFactorItem
            icon={<Percent className="w-4 h-4 text-red-600" />}
            label="Contribution"
            value={scaleFactors.contribution}
            description="Entity's percentage contribution to the total problem"
            showProgress={true}
            progressColor={getProgressColor(scaleFactors.contribution)}
            valueColor={getScoreColor(scaleFactors.contribution)}
          />
          
          {/* Duration */}
          <div className="flex items-start gap-3 p-3 rounded-lg bg-slate-50/50 dark:bg-slate-800/50 backdrop-blur-sm">
            <div className="flex-shrink-0 p-2 rounded-full bg-white/80 dark:bg-slate-700/80 shadow-sm">
              <Clock className="w-4 h-4 text-amber-600" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h4 className="text-sm font-medium text-slate-900 dark:text-slate-100">
                  Duration
                </h4>
                <Badge 
                  variant="outline" 
                  className={cn("text-white border-0", getDurationColor(scaleFactors.duration))}
                >
                  {getDurationLabel(scaleFactors.duration)}
                </Badge>
              </div>
              <p className="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
                How long the impact is expected to persist
              </p>
            </div>
          </div>
          
          {/* Reversibility */}
          <ScaleFactorItem
            icon={<RotateCcw className="w-4 h-4 text-teal-600" />}
            label="Reversibility"
            value={scaleFactors.reversibility}
            description="How easily the impact can be undone or reversed"
            showProgress={true}
            progressColor={getProgressColor(100 - scaleFactors.reversibility)} // Invert for reversibility
            valueColor={getScoreColor(100 - scaleFactors.reversibility)}
          />
          
          {/* Scope */}
          <ScaleFactorItem
            icon={<Zap className="w-4 h-4 text-yellow-600" />}
            label="Scope"
            value={scaleFactors.scope}
            description="Geographic or demographic reach of the impact"
            showProgress={true}
            progressColor={getProgressColor(scaleFactors.scope)}
            valueColor={getScoreColor(scaleFactors.scope)}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default ScaleFactorsDisplay
