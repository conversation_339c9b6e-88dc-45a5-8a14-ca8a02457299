import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report Section Loading Behavior', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should load content only once when extension has no content initially', async ({ page }) => {
    // Track API calls to report endpoints
    const apiCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      // Mock successful response
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content</p>',
          citations: []
        })
      })
    })

    // Create a new document from template
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait a bit for the page to initialize
    await page.waitForTimeout(2000)

    // Wait for report sections to be visible - try multiple selectors
    let reportSectionVisible = false;
    try {
      await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 5000 })
      reportSectionVisible = true;
    } catch (error) {
      try {
        await expect(page.locator('[data-type="reportSection"]').first()).toBeVisible({ timeout: 5000 })
        reportSectionVisible = true;
        console.log('Using [data-type="reportSection"] selector for report sections')
      } catch (altError) {
        await expect(page.locator('report-section').first()).toBeVisible({ timeout: 5000 })
        reportSectionVisible = true;
        console.log('Using report-section element selector for report sections')
      }
    }

    // Wait a bit for any loading to complete
    await page.waitForTimeout(3000)

    // Count initial API calls - each section should load once
    const initialCallCount = apiCalls.length
    expect(initialCallCount).toBeGreaterThan(0)

    // Wait another few seconds to ensure no additional calls are made
    await page.waitForTimeout(3000)
    
    // Should not have made additional calls
    expect(apiCalls.length).toBe(initialCallCount)
    
    console.log('API calls made:', apiCalls)
  })

  test.skip('should reload content when entity changes and auto-save first', async ({ page }) => {
    // Track API calls and auto-save calls
    const apiCalls: string[] = []
    const saveCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content for entity</p>',
          citations: []
        })
      })
    })

    // Track document save calls
    page.route('**/api/documents/**', route => {
      if (route.request().method() === 'PATCH' || route.request().method() === 'PUT') {
        saveCalls.push(route.request().url())
      }
      route.fulfill({
        status: 200,
        body: JSON.stringify({ success: true })
      })
    })

    // Create a new document
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait for initial load
    await page.waitForTimeout(2000)

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // This test now focuses on run changes only
    // Entity selection has been removed from the editor page
  })

  test.skip('should reload content when run changes', async ({ page }) => {
    const apiCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content for run</p>',
          citations: []
        })
      })
    })

    // Create a new document
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait for initial load
    await page.waitForTimeout(2000)

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // This test needs to be updated since run selection has also been removed from the editor page
    // For now, we'll just verify that content loads initially
    expect(initialCallCount).toBeGreaterThan(0)
  })

  test('should not reload content on component re-renders when entity/run unchanged', async ({ page }) => {
    const apiCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content</p>',
          citations: []
        })
      })
    })

    // Create a new document
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait for initial load
    await page.waitForTimeout(2000)

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // Trigger some interactions that might cause re-renders but shouldn't reload content
    
    // Click in the editor to focus it (might cause re-renders)
    await page.click('.ProseMirror')
    await page.waitForTimeout(1000)
    
    // Type some text (should not affect report sections)
    await page.keyboard.type('Some additional text')
    await page.waitForTimeout(2000)

    // Should not have made additional API calls
    expect(apiCalls.length).toBe(initialCallCount)
  })

  test('should preserve existing content when manually refreshing', async ({ page }) => {
    const apiCalls: string[] = []
    let callCounter = 0
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      callCounter++
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: `<p>Test report content - call ${callCounter}</p>`,
          citations: []
        })
      })
    })

    // Create a new document
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait for initial load
    await page.waitForTimeout(2000)

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // Manually refresh a report section - try multiple selectors
    let reportSection;
    try {
      reportSection = page.locator('.report-section').first()
      await expect(reportSection).toBeVisible({ timeout: 5000 })
    } catch (error) {
      try {
        reportSection = page.locator('[data-type="reportSection"]').first()
        await expect(reportSection).toBeVisible({ timeout: 5000 })
        console.log('Using [data-type="reportSection"] selector for refresh test')
      } catch (altError) {
        reportSection = page.locator('report-section').first()
        await expect(reportSection).toBeVisible({ timeout: 5000 })
        console.log('Using report-section element selector for refresh test')
      }
    }
    
    // Click the menu trigger
    const menuTrigger = reportSection.locator('[data-testid="report-section-menu-trigger"]')
    if (await menuTrigger.isVisible()) {
      await menuTrigger.click()
      
      // Click refresh
      await page.click('text=Refresh')
      
      // Wait for refresh to complete
      await page.waitForTimeout(3000)
      
      // Should have made one additional API call
      expect(apiCalls.length).toBe(initialCallCount + 1)
    }
  })
})
