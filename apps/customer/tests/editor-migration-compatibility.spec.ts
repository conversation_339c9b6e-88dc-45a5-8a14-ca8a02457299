import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Migration Compatibility Tests', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Enable detailed console logging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test.describe('Backward Compatibility', () => {
    test('should maintain API compatibility for existing components', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Test that old component selectors still work
      await page.click('button[title="Insert Report Section"]')
      
      // Verify component is inserted with expected structure
      const component = page.locator('[data-component-type="report-section"]').first()
      await expect(component).toBeVisible()

      // Check that old attribute patterns still exist
      await expect(component).toHaveAttribute('data-component-id')
      await expect(component).toHaveAttribute('data-status')
      await expect(component).toHaveAttribute('data-component-type', 'report-section')

      // Verify component reaches expected state
      await expect(component).toHaveAttribute('data-status', 'loaded', { timeout: 10000 })
    })

    test('should handle existing document structure correctly', async ({ page }) => {
      // Create a document with the old system structure
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Insert components using existing UI
      await page.click('button[title="Insert Report Group"]')
      const group = page.locator('[data-component-type="report-group"]').first()
      await expect(group).toBeVisible()

      await group.click()
      await page.click('button[title="Insert Report Section"]')
      await page.click('button[title="Insert Report Section"]')

      // Verify nested structure works
      await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(2)
      
      // Check parent-child relationships are maintained
      const sections = page.locator('[data-component-type="report-section"]')
      for (let i = 0; i < 2; i++) {
        const section = sections.nth(i)
        await expect(section).toHaveAttribute('data-parent-id')
      }

      // Group should show correct status based on children
      await expect(group).toHaveAttribute('data-status', 'loaded', { timeout: 15000 })
    })

    test('should preserve existing keyboard shortcuts and interactions', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Test existing keyboard shortcuts still work
      await page.locator('.ProseMirror').click()
      
      // Test Ctrl+S for save
      await page.keyboard.press('Control+s')
      await expect(page.locator('[data-testid="save-indicator"]')).toContainText(/saved|up to date/i)

      // Test component insertion shortcuts (if they exist)
      await page.keyboard.press('Control+Shift+s') // Assuming this is a section shortcut
      
      // Should insert component
      const componentCount = await page.locator('[data-component-type]').count()
      expect(componentCount).toBeGreaterThan(0)
    })
  })

  test.describe('Feature Parity', () => {
    test('should maintain all existing component functionality', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Test all component types can be inserted
      const componentTypes = [
        { button: 'Insert Report Section', type: 'report-section' },
        { button: 'Insert Report Group', type: 'report-group' }
      ]

      for (const { button, type } of componentTypes) {
        await page.click(`button[title="${button}"]`)
        await expect(page.locator(`[data-component-type="${type}"]`)).toBeVisible()
      }

      // Test component interactions work
      const section = page.locator('[data-component-type="report-section"]').first()
      
      // Click to select
      await section.click()
      await expect(section).toHaveClass(/selected|focused/)

      // Right-click for context menu (if implemented)
      await section.click({ button: 'right' })
      
      // Test component deletion
      await section.click()
      await page.keyboard.press('Delete')
      
      // Component should be removed or marked for deletion
      await expect(section).not.toBeVisible()
    })

    test('should maintain entity and run selection functionality', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Test entity selector functionality
      const entitySelector = page.locator('[data-testid="entity-selector"]')
      if (await entitySelector.isVisible()) {
        await entitySelector.click()
        
        // Should show entity options
        await expect(page.locator('[data-testid="entity-option"]')).toBeVisible()
        
        // Select an entity
        await page.locator('[data-testid="entity-option"]').first().click()
        
        // Insert a component that depends on entity
        await page.click('button[title="Insert Report Section"]')
        const component = page.locator('[data-component-type="report-section"]').first()
        
        // Component should load with selected entity
        await expect(component).toHaveAttribute('data-status', 'loaded', { timeout: 15000 })
      }
    })

    test('should maintain save and versioning functionality', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add content
      await page.locator('.ProseMirror').fill('Test content for versioning')
      
      // Insert a component
      await page.click('button[title="Insert Report Section"]')
      await expect(page.locator('[data-component-type="report-section"]')).toBeVisible()

      // Test auto-save functionality
      await page.waitForTimeout(3000) // Wait for auto-save
      await expect(page.locator('[data-testid="save-indicator"]')).toContainText(/saved|up to date/i)

      // Test manual save
      await page.keyboard.press('Control+s')
      
      // Test version creation (if UI exists)
      const versionButton = page.locator('[data-testid="create-version"]')
      if (await versionButton.isVisible()) {
        await versionButton.click()
        
        await page.fill('[data-testid="version-summary"]', 'Test version')
        await page.click('[data-testid="create-version-confirm"]')
        
        await expect(page.locator('[data-testid="version-created-toast"]')).toBeVisible()
      }
    })
  })

  test.describe('Performance Regression Tests', () => {
    test('should not degrade performance compared to baseline', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Baseline performance test - document loading
      const loadStartTime = await page.evaluate(() => performance.now())
      
      // Reload page to test loading performance
      await page.reload()
      await testUtils.waitForEditor()
      
      const loadEndTime = await page.evaluate(() => performance.now())
      const loadTime = loadEndTime - loadStartTime

      // Should load within reasonable time (10 seconds)
      expect(loadTime).toBeLessThan(10000)

      // Test component insertion performance
      const insertStartTime = await page.evaluate(() => performance.now())
      
      await page.click('button[title="Insert Report Section"]')
      await expect(page.locator('[data-component-type="report-section"]')).toBeVisible()
      
      const insertEndTime = await page.evaluate(() => performance.now())
      const insertTime = insertEndTime - insertStartTime

      // Should insert component quickly (under 2 seconds)
      expect(insertTime).toBeLessThan(2000)

      console.log(`Performance metrics - Load: ${loadTime}ms, Insert: ${insertTime}ms`)
    })

    test('should handle same workload as old system efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Create the same complex document structure that would have been created with old system
      const startTime = await page.evaluate(() => performance.now())

      // Create nested structure: Group -> 5 sections, Group -> 3 sections
      await page.click('button[title="Insert Report Group"]')
      let group = page.locator('[data-component-type="report-group"]').first()
      await expect(group).toBeVisible()
      
      await group.click()
      for (let i = 0; i < 5; i++) {
        await page.click('button[title="Insert Report Section"]')
      }

      await page.click('button[title="Insert Report Group"]')
      group = page.locator('[data-component-type="report-group"]').nth(1)
      await expect(group).toBeVisible()
      
      await group.click()
      for (let i = 0; i < 3; i++) {
        await page.click('button[title="Insert Report Section"]')
      }

      // Wait for all components to load
      await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(8)
      await expect(page.locator('[data-component-type="report-group"]')).toHaveCount(2)

      // Wait for all to reach loaded state
      const sections = page.locator('[data-component-type="report-section"]')
      for (let i = 0; i < 8; i++) {
        await expect(sections.nth(i)).toHaveAttribute('data-status', 'loaded', { timeout: 30000 })
      }

      const groups = page.locator('[data-component-type="report-group"]')
      for (let i = 0; i < 2; i++) {
        await expect(groups.nth(i)).toHaveAttribute('data-status', 'loaded', { timeout: 30000 })
      }

      const endTime = await page.evaluate(() => performance.now())
      const totalTime = endTime - startTime

      // Should complete complex document creation efficiently (under 1 minute)
      expect(totalTime).toBeLessThan(60000)

      console.log(`Created complex document (2 groups, 8 sections) in ${totalTime}ms`)
    })
  })

  test.describe('Error Handling Parity', () => {
    test('should handle errors the same way as old system', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Mock API error response
      await page.route('**/api/report/entity/**', route => {
        route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Test error message' })
        })
      })

      // Insert component that will fail
      await page.click('button[title="Insert Report Section"]')
      const component = page.locator('[data-component-type="report-section"]').first()
      await expect(component).toBeVisible()

      // Should show error state
      await expect(component).toHaveAttribute('data-status', 'error', { timeout: 10000 })

      // Should show error UI elements (same as old system)
      await expect(component.locator('[data-testid="error-message"]')).toBeVisible()
      
      // Should have retry functionality
      const retryButton = component.locator('[data-testid="retry-button"]')
      if (await retryButton.isVisible()) {
        await expect(retryButton).toBeVisible()
      }

      // Error should not crash the entire editor
      await expect(page.locator('.ProseMirror')).toBeVisible()
      await expect(page.locator('[data-testid="editor-toolbar"]')).toBeVisible()
    })

    test('should recover from errors gracefully', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      let requestCount = 0

      // Mock API to fail first request, succeed on second
      await page.route('**/api/report/entity/**', route => {
        requestCount++
        if (requestCount === 1) {
          route.fulfill({
            status: 500,
            body: JSON.stringify({ error: 'Temporary failure' })
          })
        } else {
          route.fulfill({
            status: 200,
            body: JSON.stringify({
              text: '<p>Recovered content</p>',
              citations: []
            })
          })
        }
      })

      // Insert component
      await page.click('button[title="Insert Report Section"]')
      const component = page.locator('[data-component-type="report-section"]').first()
      
      // Should show error initially
      await expect(component).toHaveAttribute('data-status', 'error', { timeout: 5000 })

      // Retry should work
      await component.locator('[data-testid="retry-button"]').click()
      
      // Should recover to loaded state
      await expect(component).toHaveAttribute('data-status', 'loaded', { timeout: 10000 })
      
      // Should show recovered content
      await expect(component).toContainText('Recovered content')
    })
  })
})