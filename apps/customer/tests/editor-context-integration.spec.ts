import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Context Integration Tests', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test.describe('Component Registration and Status Management', () => {
    test('should register components and manage their status correctly', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Test component registration by inserting a report section
      await page.click('button[title="Insert Report Section"]')
      
      // Wait for the component to be inserted and registered
      await expect(page.locator('[data-component-type="report-section"]')).toBeVisible()

      // Check that component status is managed correctly
      // The component should start as 'loading' or 'registering'
      const component = page.locator('[data-component-type="report-section"]').first()
      
      // Wait for component to load
      await expect(component).toHaveAttribute('data-status', /loaded|loading|registering/)
      
      // Eventually should reach loaded state
      await expect(component).toHaveAttribute('data-status', 'loaded', { timeout: 10000 })
    })

    test('should handle component dependencies correctly', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Insert a report group first
      await page.click('button[title="Insert Report Group"]')
      await expect(page.locator('[data-component-type="report-group"]')).toBeVisible()

      // Insert a report section inside the group
      const group = page.locator('[data-component-type="report-group"]').first()
      await group.click()
      
      // Insert section inside group
      await page.click('button[title="Insert Report Section"]')
      
      // Wait for both components to be registered
      await expect(page.locator('[data-component-type="report-section"]')).toBeVisible()

      // Check that the group status reflects its children
      const groupStatus = await group.getAttribute('data-status')
      const sectionStatus = await page.locator('[data-component-type="report-section"]').first().getAttribute('data-status')

      // Group should be loading if any child is loading
      if (sectionStatus === 'loading') {
        expect(groupStatus).toBe('loading')
      }
    })

    test('should update group status when child components change', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Create a complex structure with nested groups
      await page.click('button[title="Insert Report Group"]')
      const parentGroup = page.locator('[data-component-type="report-group"]').first()
      await expect(parentGroup).toBeVisible()

      // Add multiple sections to the group
      await parentGroup.click()
      await page.click('button[title="Insert Report Section"]')
      await page.click('button[title="Insert Report Section"]')

      // Wait for all components to be registered
      await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(2)

      // Monitor group status changes
      const groupStatusChanges: string[] = []
      
      // Use page.evaluate to monitor attribute changes
      await page.evaluate(() => {
        const group = document.querySelector('[data-component-type="report-group"]') as HTMLElement
        if (group) {
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (mutation.type === 'attributes' && mutation.attributeName === 'data-status') {
                const status = (mutation.target as HTMLElement).getAttribute('data-status')
                if (status) {
                  (window as any).groupStatusChanges = (window as any).groupStatusChanges || []
                  ;(window as any).groupStatusChanges.push(status)
                }
              }
            })
          })
          observer.observe(group, { attributes: true, attributeFilter: ['data-status'] })
        }
      })

      // Wait for all components to load
      await expect(page.locator('[data-component-type="report-section"]')).toHaveAttribute('data-status', 'loaded')
      await expect(parentGroup).toHaveAttribute('data-status', 'loaded')

      // Verify that group status changes were tracked
      const statusChanges = await page.evaluate(() => (window as any).groupStatusChanges || [])
      expect(statusChanges.length).toBeGreaterThan(0)
    })
  })

  test.describe('Memory Management', () => {
    test('should clean up resources when components are removed', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Insert a component
      await page.click('button[title="Insert Report Section"]')
      await expect(page.locator('[data-component-type="report-section"]')).toBeVisible()

      // Check that memory tracking is working by examining console logs
      const consoleLogs: string[] = []
      page.on('console', msg => {
        if (msg.text().includes('MemoryManager') || msg.text().includes('cleanup')) {
          consoleLogs.push(msg.text())
        }
      })

      // Remove the component
      const component = page.locator('[data-component-type="report-section"]').first()
      await component.click()
      await page.keyboard.press('Delete')

      // Component should be removed
      await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(0)

      // Give time for cleanup to occur
      await page.waitForTimeout(1000)

      // Check that cleanup logs were generated
      expect(consoleLogs.some(log => log.includes('cleanup'))).toBe(true)
    })

    test('should handle page navigation without memory leaks', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Insert multiple components
      await page.click('button[title="Insert Report Section"]')
      await page.click('button[title="Insert Report Group"]')
      await expect(page.locator('[data-component-type="report-section"]')).toBeVisible()
      await expect(page.locator('[data-component-type="report-group"]')).toBeVisible()

      // Track memory usage
      const memoryBefore = await page.evaluate(() => {
        return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0
      })

      // Navigate away and back
      await page.goto('/customer/dashboard')
      await page.waitForLoadState('networkidle')
      
      await page.goto(`/customer/document/${documentId}`)
      await testUtils.waitForEditor()

      // Check memory after navigation
      const memoryAfter = await page.evaluate(() => {
        return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0
      })

      // Memory usage should not have grown significantly (allowing for normal variance)
      if (memoryBefore > 0 && memoryAfter > 0) {
        const memoryGrowth = (memoryAfter - memoryBefore) / memoryBefore
        expect(memoryGrowth).toBeLessThan(0.5) // Less than 50% growth
      }
    })
  })

  test.describe('Document Operations', () => {
    test('should save and load documents correctly with new context', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add some content
      await page.locator('.ProseMirror').fill('Test content for document operations')
      
      // Insert a component
      await page.click('button[title="Insert Report Section"]')
      await expect(page.locator('[data-component-type="report-section"]')).toBeVisible()

      // Wait for auto-save to occur
      await page.waitForTimeout(2000)

      // Check that save indicator shows document is saved
      await expect(page.locator('[data-testid="save-indicator"]')).toContainText(/saved|up to date/i)

      // Reload the page to test loading
      await page.reload()
      await testUtils.waitForEditor()

      // Content should be restored
      await expect(page.locator('.ProseMirror')).toContainText('Test content for document operations')
      await expect(page.locator('[data-component-type="report-section"]')).toBeVisible()
    })

    test('should handle version creation and restoration', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add initial content
      await page.locator('.ProseMirror').fill('Initial version content')
      await page.waitForTimeout(1000)

      // Create a manual version
      await page.click('[data-testid="version-menu-trigger"]')
      await page.click('[data-testid="create-version"]')
      
      // Fill in version details
      await page.fill('[data-testid="version-summary"]', 'Initial version')
      await page.click('[data-testid="create-version-confirm"]')

      // Wait for version to be created
      await expect(page.locator('[data-testid="version-created-toast"]')).toBeVisible()

      // Modify content
      await page.locator('.ProseMirror').fill('Modified content')
      await page.waitForTimeout(1000)

      // Restore previous version
      await page.click('[data-testid="version-menu-trigger"]')
      await page.click('[data-testid="version-history"]')
      
      // Select the first version and restore
      await page.click('[data-testid="version-item"]:first-child [data-testid="restore-version"]')
      await page.click('[data-testid="confirm-restore"]')

      // Content should be restored
      await expect(page.locator('.ProseMirror')).toContainText('Initial version content')
    })
  })

  test.describe('Performance Optimizations', () => {
    test('should debounce group status updates efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Track update calls
      const updateCalls: number[] = []
      
      await page.evaluate(() => {
        const originalConsoleLog = console.log
        console.log = (...args: any[]) => {
          if (args[0] && args[0].includes && args[0].includes('Group status update')) {
            (window as any).updateCalls = (window as any).updateCalls || []
            ;(window as any).updateCalls.push(Date.now())
          }
          originalConsoleLog.apply(console, args)
        }
      })

      // Create a group with multiple children
      await page.click('button[title="Insert Report Group"]')
      const group = page.locator('[data-component-type="report-group"]').first()
      await expect(group).toBeVisible()

      // Rapidly add multiple sections
      await group.click()
      for (let i = 0; i < 5; i++) {
        await page.click('button[title="Insert Report Section"]')
        await page.waitForTimeout(50) // Small delay between additions
      }

      // Wait for all debounced updates to complete
      await page.waitForTimeout(1000)

      // Check that updates were debounced (fewer updates than actions)
      const calls = await page.evaluate(() => (window as any).updateCalls || [])
      expect(calls.length).toBeLessThan(5) // Should be fewer than the number of components added
    })

    test('should prevent unnecessary re-renders', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add performance marks
      await page.evaluate(() => {
        (window as any).renderCount = 0
        
        // Mock React's render tracking
        const originalConsoleLog = console.log
        console.log = (...args: any[]) => {
          if (args[0] && args[0].includes && args[0].includes('Component render')) {
            (window as any).renderCount++
          }
          originalConsoleLog.apply(console, args)
        }
      })

      // Insert component
      await page.click('button[title="Insert Report Section"]')
      await expect(page.locator('[data-component-type="report-section"]')).toBeVisible()

      // Perform actions that should not cause unnecessary re-renders
      await page.locator('.ProseMirror').click()
      await page.keyboard.type('Some text')
      
      // Wait for renders to stabilize
      await page.waitForTimeout(1000)

      const renderCount = await page.evaluate(() => (window as any).renderCount || 0)
      
      // Should have reasonable number of renders (exact number depends on implementation)
      expect(renderCount).toBeLessThan(20) // Arbitrary reasonable threshold
    })
  })

  test.describe('Error Handling and Recovery', () => {
    test('should handle component loading errors gracefully', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Mock API failure for report sections
      await page.route('**/api/report/entity/**', route => {
        route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Internal server error' })
        })
      })

      // Insert a report section
      await page.click('button[title="Insert Report Section"]')
      
      // Component should be visible but in error state
      const component = page.locator('[data-component-type="report-section"]').first()
      await expect(component).toBeVisible()
      
      // Should show error state
      await expect(component).toHaveAttribute('data-status', 'error', { timeout: 10000 })
      
      // Error message should be displayed
      await expect(component.locator('[data-testid="error-message"]')).toBeVisible()
    })

    test('should recover from transient errors', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      let callCount = 0
      
      // Mock API to fail first call, succeed on retry
      await page.route('**/api/report/entity/**', route => {
        callCount++
        if (callCount === 1) {
          route.fulfill({
            status: 500,
            body: JSON.stringify({ error: 'Temporary error' })
          })
        } else {
          route.fulfill({
            status: 200,
            body: JSON.stringify({
              text: '<p>Test report content</p>',
              citations: []
            })
          })
        }
      })

      // Insert a report section
      await page.click('button[title="Insert Report Section"]')
      const component = page.locator('[data-component-type="report-section"]').first()
      await expect(component).toBeVisible()

      // Should initially show error
      await expect(component).toHaveAttribute('data-status', 'error', { timeout: 5000 })

      // Click retry button
      await component.locator('[data-testid="retry-button"]').click()

      // Should recover to loaded state
      await expect(component).toHaveAttribute('data-status', 'loaded', { timeout: 10000 })
    })
  })
})