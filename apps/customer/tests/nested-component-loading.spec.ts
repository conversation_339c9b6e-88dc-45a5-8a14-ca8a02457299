import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Nested Component Loading Tests', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console [${msg.type()}]:`, msg.text());
    });

    // Log uncaught errors
    page.on('pageerror', exception => {
      console.log('Page error:', exception.toString());
    });

    // Log network requests for debugging
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        console.log('API Request:', request.method(), request.url());
      }
    });

    page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log('API Response:', response.status(), response.url());
      }
    });

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should load report-section with nested charts and citations', async ({ page }) => {
    // Mock API response with charts and citations
    const mockResponse = {
      text: `
        <h2>Environmental Impact Analysis</h2>
        <p>This analysis shows significant environmental concerns [^123456].</p>
        
        <chart>
        {
          "title": { "text": "Carbon Emissions Trend" },
          "xAxis": { "type": "category", "data": ["2020", "2021", "2022", "2023"] },
          "yAxis": { "type": "value" },
          "series": [{
            "data": [120, 132, 101, 134],
            "type": "line"
          }]
        }
        </chart>
        
        <p>The data indicates a concerning trend [^789012] that requires immediate attention.</p>
        
        <chart>
        {
          "title": { "text": "Water Usage by Quarter" },
          "tooltip": { "trigger": "item" },
          "series": [{
            "name": "Water Usage",
            "type": "pie",
            "data": [
              { "value": 1048, "name": "Q1" },
              { "value": 735, "name": "Q2" },
              { "value": 580, "name": "Q3" },
              { "value": 484, "name": "Q4" }
            ]
          }]
        }
        </chart>
      `,
      citations: [
        {
          doc_page_id: 123456,
          title: "Environmental Impact Report 2023",
          url: "https://example.com/report1",
          domain: "Environmental"
        },
        {
          doc_page_id: 789012,
          title: "Water Usage Analysis",
          url: "https://example.com/report2", 
          domain: "Environmental"
        }
      ]
    }

    // Mock the report API endpoint
    page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      })
    })

    // Create document from template using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for entity selector and auto-selection
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities') && !text.includes('Select entity')
    }, { timeout: 15000 })

    // Wait for report sections to load
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 10000 })
    
    // Wait for content to load
    await page.waitForTimeout(3000)

    // Verify charts are rendered (using correct selector from chart extension)
    const charts = page.locator('chart, [data-type="chart"]')
    await expect(charts).toHaveCount(2)

    // Verify chart content is properly rendered
    await expect(charts.first()).toBeVisible()
    await expect(charts.nth(1)).toBeVisible()

    // Verify citations are present in the content (using correct selector from citation extension)
    const citations = page.locator('.citation-wrapper')
    await expect(citations).toHaveCount(2)

    // Verify citation content
    await expect(page.locator('text=Environmental Impact Report 2023')).toBeVisible()
    await expect(page.locator('text=Water Usage Analysis')).toBeVisible()

    // Verify the text content is properly rendered
    await expect(page.locator('text=Environmental Impact Analysis')).toBeVisible()
    await expect(page.locator('text=This analysis shows significant environmental concerns')).toBeVisible()
  })

  test('should handle malformed chart JSON gracefully', async ({ page }) => {
    // Mock API response with malformed chart JSON
    const mockResponse = {
      text: `
        <h2>Analysis with Broken Chart</h2>
        <p>This section contains a malformed chart.</p>
        
        <chart>
        {
          "title": { "text": "Broken Chart" 
          "xAxis": { "type": "category", "data": ["A", "B"] },
          // Missing closing brace and comma
        </chart>
        
        <p>Content after the broken chart should still render.</p>
      `,
      citations: []
    }

    page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      })
    })

    // Create document using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for loading
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    await page.waitForTimeout(3000)

    // Verify chart error is displayed gracefully (check if chart renders or shows error)
    const charts = page.locator('chart, [data-type="chart"]')
    const chartCount = await charts.count()

    // Chart should not render with malformed JSON
    expect(chartCount).toBe(0)

    // Verify other content still renders
    await expect(page.locator('text=Analysis with Broken Chart')).toBeVisible()
    await expect(page.locator('text=Content after the broken chart should still render')).toBeVisible()
  })

  test('should load nested report-summary with dependencies', async ({ page }) => {
    // Track which components have been called
    const apiCalls: string[] = []
    
    // Mock report section responses - match the actual ESG template endpoints
    page.route('**/api/report/entity/*/harm/model/*/section/*', route => {
      apiCalls.push(route.request().url())
      const sectionId = route.request().url().split('/').pop()?.split('?')[0]

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify({
          text: `<h3>Section ${sectionId}</h3><p>Content for section ${sectionId} with analysis [^${Math.floor(Math.random() * 1000000)}].</p>`,
          citations: [{
            doc_page_id: Math.floor(Math.random() * 1000000),
            title: `Citation for ${sectionId}`,
            url: `https://example.com/${sectionId}`,
            domain: "Environmental"
          }]
        })
      })
    })

    // Mock transparency endpoint
    page.route('**/api/report/entity/*/transparency*', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify({
          text: `<h3>Transparency Analysis</h3><p>Transparency analysis content [^${Math.floor(Math.random() * 1000000)}].</p>`,
          citations: [{
            doc_page_id: Math.floor(Math.random() * 1000000),
            title: `Transparency Citation`,
            url: `https://example.com/transparency`,
            domain: "Governance"
          }]
        })
      })
    })

    // Mock reliability endpoint
    page.route('**/api/report/entity/*/reliability*', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify({
          text: `<h3>Reliability Analysis</h3><p>Reliability analysis content [^${Math.floor(Math.random() * 1000000)}].</p>`,
          citations: [{
            doc_page_id: Math.floor(Math.random() * 1000000),
            title: `Reliability Citation`,
            url: `https://example.com/reliability`,
            domain: "Governance"
          }]
        })
      })
    })

    // Mock summary endpoint
    page.route('**/api/report/summarize', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify({
          text: `<h2>Executive Summary</h2><p>This summary consolidates findings from multiple sections [^999999].</p>`,
          citations: [{
            doc_page_id: 999999,
            title: "Summary Citation",
            url: "https://example.com/summary",
            domain: "Governance"
          }]
        })
      })
    })

    // Create document using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for entity selection - increased timeout for slow loading
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 30000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 60000 })

    // Wait for initial components to load - increased timeout
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 30000 })
    await expect(page.locator('.report-summary').first()).toBeVisible({ timeout: 30000 })

    // Wait for sections to finish loading first - increased timeout
    await page.waitForTimeout(30000)

    // Verify sections loaded before summary
    const sectionCalls = apiCalls.filter(url => url.includes('/section/'))
    const summaryCalls = apiCalls.filter(url => url.includes('/summarize'))

    expect(sectionCalls.length).toBeGreaterThan(0)

    // Wait for summary to complete (it should wait for dependencies) - increased timeout
    await page.waitForTimeout(60000)
    
    // Verify summary was called after sections
    expect(summaryCalls.length).toBeGreaterThan(0)

    // Verify summary content is displayed (use .first() to avoid strict mode violations)
    await expect(page.locator('text=Executive Summary').first()).toBeVisible()
    await expect(page.locator('text=This summary consolidates findings').first()).toBeVisible()

    // Verify citations from both sections and summary are present
    const allCitations = page.locator('.citation-wrapper')
    await expect(allCitations.first()).toBeVisible()
  })

  test('should handle nested report-group with multiple sections and charts', async ({ page }) => {
    // Mock responses for multiple sections within a group
    page.route('**/api/report/entity/*/harm/model/*/section/*', route => {
      const url = route.request().url()
      const sectionId = url.split('/').pop()?.split('?')[0] || 'unknown'

      // Different content for different sections
      const responses: Record<string, any> = {
        '13_climate_action': {
          text: `
            <h3>Climate Action Analysis</h3>
            <p>Climate initiatives show mixed results [^111111].</p>
            <chart>
            {
              "title": { "text": "CO2 Reduction Progress" },
              "xAxis": { "type": "category", "data": ["2020", "2021", "2022", "2023"] },
              "yAxis": { "type": "value" },
              "series": [{ "data": [100, 85, 70, 60], "type": "bar" }]
            }
            </chart>
            <p>Significant progress in carbon reduction [^222222].</p>
          `,
          citations: [
            { doc_page_id: 111111, title: "Climate Report 2023", url: "https://example.com/climate", domain: "Environmental" },
            { doc_page_id: 222222, title: "Carbon Analysis", url: "https://example.com/carbon", domain: "Environmental" }
          ]
        },
        '2_zero_hunger': {
          text: `
            <h3>Zero Hunger Initiative</h3>
            <p>Food security programs are expanding [^333333].</p>
            <chart>
            {
              "title": { "text": "Food Program Reach" },
              "tooltip": { "trigger": "item" },
              "series": [{
                "type": "pie",
                "data": [
                  { "value": 40, "name": "Urban" },
                  { "value": 60, "name": "Rural" }
                ]
              }]
            }
            </chart>
          `,
          citations: [
            { doc_page_id: 333333, title: "Food Security Report", url: "https://example.com/food", domain: "Social" }
          ]
        }
      }

      const response = responses[sectionId] || {
        text: `<h3>Section ${sectionId}</h3><p>Default content for ${sectionId}.</p>`,
        citations: []
      }

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(response)
      })
    })

    // Create document using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for entity selection
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for report groups and sections to load
    await expect(page.locator('.report-group').first()).toBeVisible({ timeout: 10000 })
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 10000 })

    // Wait for content loading to complete
    await page.waitForTimeout(5000)

    // Verify multiple charts are rendered within the group
    const charts = page.locator('chart, [data-type="chart"]')
    await expect(charts).toHaveCount(2, { timeout: 10000 })

    // Verify specific chart content (use .first() to avoid strict mode violations)
    await expect(page.locator('text=CO2 Reduction Progress').first()).toBeVisible()
    await expect(page.locator('text=Food Program Reach').first()).toBeVisible()

    // Verify citations from different sections
    await expect(page.locator('text=Climate Report 2023')).toBeVisible()
    await expect(page.locator('text=Food Security Report')).toBeVisible()

    // Verify section headers are present
    await expect(page.locator('text=Climate Action Analysis')).toBeVisible()
    await expect(page.locator('text=Zero Hunger Initiative')).toBeVisible()

    // Verify the report group structure is maintained
    const reportGroup = page.locator('.report-group').first()
    const sectionsInGroup = reportGroup.locator('.report-section')
    await expect(sectionsInGroup).toHaveCount(2, { timeout: 5000 })
  })

  test('should handle citation loading and reference list generation', async ({ page }) => {
    // Mock response with multiple citations
    const mockResponse = {
      text: `
        <h2>Comprehensive Analysis</h2>
        <p>Multiple studies [^100001] indicate significant trends [^100002].</p>
        <p>Further research [^100003] supports these findings [^100004].</p>
        <p>The conclusion [^100005] aligns with previous work [^100001].</p>
      `,
      citations: [
        { doc_page_id: 100001, title: "Primary Research Study", url: "https://example.com/study1", domain: "Environmental" },
        { doc_page_id: 100002, title: "Trend Analysis Report", url: "https://example.com/trend", domain: "Social" },
        { doc_page_id: 100003, title: "Supporting Evidence", url: "https://example.com/evidence", domain: "Governance" },
        { doc_page_id: 100004, title: "Validation Study", url: "https://example.com/validation", domain: "Environmental" },
        { doc_page_id: 100005, title: "Conclusion Paper", url: "https://example.com/conclusion", domain: "Social" }
      ]
    }

    page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      })
    })

    // Create document using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for loading
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    await page.waitForTimeout(3000)

    // Verify all citations are rendered
    const citations = page.locator('.citation-wrapper')
    await expect(citations).toHaveCount(6) // 5 unique citations, but one is repeated

    // Verify citation content
    await expect(page.locator('text=Primary Research Study')).toBeVisible()
    await expect(page.locator('text=Trend Analysis Report')).toBeVisible()
    await expect(page.locator('text=Supporting Evidence')).toBeVisible()
    await expect(page.locator('text=Validation Study')).toBeVisible()
    await expect(page.locator('text=Conclusion Paper')).toBeVisible()

    // Verify references section is generated (if implemented)
    const referencesSection = page.locator('[data-type="references"]')
    await expect(referencesSection).toBeVisible()

    // Check that references list contains unique citations
    const referenceItems = referencesSection.locator('.citation')
    await expect(referenceItems).toHaveCount(5)
  })

  test('should handle loading states and error recovery for nested components', async ({ page }) => {
    let callCount = 0

    // Mock API with failures
    page.route('**/api/report/**', route => {
      callCount++

      // All calls fail
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Server error' })
      })
    })

    // Create document using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for entity selection
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for initial loading attempts (which will fail)
    await page.waitForTimeout(3000)

    // Verify error state is shown
    await expect(page.locator('.error, [role="alert"]')).toBeVisible({ timeout: 10000 })

    // Manually refresh a component to trigger retry
    const reportSection = page.locator('.report-section').first()
    await expect(reportSection).toBeVisible()

    const menuTrigger = reportSection.locator('[data-testid="report-section-menu-trigger"]')
    await expect(menuTrigger).toBeVisible()
    await menuTrigger.click()
    await page.click('text=Refresh')

    // Wait for retry and expect it to still fail
    await page.waitForTimeout(3000)

    // Verify error persists
    await expect(page.locator('.error, [role="alert"]')).toBeVisible()
  })

  test('should preserve chart JSON during report-section refresh', async ({ page }) => {
    // Mock API response with simple chart JSON for debugging
    const chartJson = {
      'title': { 'text': 'Test Chart' },
      'xAxis': { 'type': 'category', 'data': ['A', 'B', 'C'] },
      'yAxis': { 'type': 'value' },
      'series': [{ 'data': [10, 20, 30], 'type': 'bar' }],
    }

    const mockResponse = {
      text: `<h2>Test Analysis</h2>
<p>This is a test with a chart.</p>

<chart>
${JSON.stringify(chartJson, null, 2)}
</chart>

<p>Chart should be visible above.</p>`,
      citations: [],
    }

    // Mock the API response for both initial load and refresh
    page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse),
      })
    })

    // Navigate to document with report sections using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for entity selection
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for initial loading
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 10000 })
    await page.waitForTimeout(5000)

    // Debug: Log what's actually in the DOM
    const htmlContent = await page.locator('.report-section').first().innerHTML()
    console.log('Report section HTML:', htmlContent.substring(0, 500))

    // Check for any chart-related elements
    const chartElements = page.locator('chart')
    const chartWrappers = page.locator('.chart-wrapper')
    const chartErrors = page.locator('.chart-error')

    console.log('Chart elements count:', await chartElements.count())
    console.log('Chart wrappers count:', await chartWrappers.count())
    console.log('Chart errors count:', await chartErrors.count())

    // If there are chart errors, log them
    if (await chartErrors.count() > 0) {
      const errorText = await chartErrors.first().textContent()
      console.log('Chart error text:', errorText)
    }

    // Verify basic content is there
    await expect(page.locator('text=Test Analysis')).toBeVisible()
    await expect(page.locator('text=Chart should be visible above')).toBeVisible()

    // Try to find charts with various selectors
    const allChartSelectors = [
      'chart',
      '[data-type="chart"]',
      '.chart-wrapper',
      '.chart-error',
      'div[data-node-type="chart"]',
    ]

    for (const selector of allChartSelectors) {
      const elements = page.locator(selector)
      const count = await elements.count()
      console.log(`Selector "${selector}": ${count} elements`)
    }
  })

  test('should handle complex nested structure with mixed content types', async ({ page }) => {
    // Mock response with mixed content: text, charts, citations, and nested structures
    const mockResponse = {
      text: `
        <h2>Complex Analysis Report</h2>
        <p>This report contains multiple content types [^600001].</p>

        <h3>Financial Performance</h3>
        <chart>
        {
          "title": { "text": "Revenue Growth" },
          "xAxis": { "type": "category", "data": ["Q1", "Q2", "Q3", "Q4"] },
          "yAxis": { "type": "value" },
          "series": [{ "data": [120, 132, 101, 134], "type": "line" }]
        }
        </chart>

        <p>Revenue trends show positive growth [^600002].</p>

        <h3>Environmental Impact</h3>
        <p>Environmental metrics require attention [^600003]:</p>
        <ul>
          <li>Carbon emissions increased by 5%</li>
          <li>Water usage decreased by 12%</li>
          <li>Waste reduction improved by 8%</li>
        </ul>

        <chart>
        {
          "title": { "text": "Environmental Metrics" },
          "tooltip": { "trigger": "axis" },
          "legend": { "data": ["Carbon", "Water", "Waste"] },
          "xAxis": { "type": "category", "data": ["Jan", "Feb", "Mar", "Apr"] },
          "yAxis": { "type": "value" },
          "series": [
            { "name": "Carbon", "type": "bar", "data": [105, 110, 108, 115] },
            { "name": "Water", "type": "bar", "data": [100, 95, 90, 88] },
            { "name": "Waste", "type": "bar", "data": [100, 98, 95, 92] }
          ]
        }
        </chart>

        <h3>Social Responsibility</h3>
        <p>Community programs expanded significantly [^600004].</p>
        <blockquote>
          <p>"Our commitment to social responsibility drives meaningful change in communities worldwide." - CEO Statement [^600005]</p>
        </blockquote>

        <h3>Governance</h3>
        <p>Board composition and oversight improved [^600006].</p>
        <table>
          <tr><th>Metric</th><th>2022</th><th>2023</th></tr>
          <tr><td>Independent Directors</td><td>60%</td><td>70%</td></tr>
          <tr><td>Board Meetings</td><td>8</td><td>10</td></tr>
        </table>
      `,
      citations: [
        { doc_page_id: 600001, title: "Annual Report 2023", url: "https://example.com/annual", domain: "Governance" },
        { doc_page_id: 600002, title: "Financial Analysis Q4", url: "https://example.com/financial", domain: "Governance" },
        { doc_page_id: 600003, title: "Environmental Impact Assessment", url: "https://example.com/environmental", domain: "Environmental" },
        { doc_page_id: 600004, title: "Social Programs Report", url: "https://example.com/social", domain: "Social" },
        { doc_page_id: 600005, title: "CEO Statement 2023", url: "https://example.com/ceo", domain: "Governance" },
        { doc_page_id: 600006, title: "Governance Review", url: "https://example.com/governance", domain: "Governance" }
      ]
    }

    page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      })
    })

    // Create document using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for loading
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    await page.waitForTimeout(3000)

    // Verify all content types are rendered correctly

    // Headers (use .first() to avoid strict mode violations)
    await expect(page.locator('text=Complex Analysis Report').first()).toBeVisible()
    await expect(page.locator('text=Financial Performance').first()).toBeVisible()
    await expect(page.locator('text=Environmental Impact').first()).toBeVisible()
    await expect(page.locator('text=Social Responsibility').first()).toBeVisible()
    await expect(page.locator('text=Governance').first()).toBeVisible()

    // Charts
    const charts = page.locator('chart, [data-type="chart"]')
    await expect(charts).toHaveCount(2)
    await expect(page.locator('text=Revenue Growth').first()).toBeVisible()
    await expect(page.locator('text=Environmental Metrics').first()).toBeVisible()

    // Citations
    const citations = page.locator('.citation-wrapper')
    await expect(citations).toHaveCount(6)
    await expect(page.locator('text=Annual Report 2023').first()).toBeVisible()
    await expect(page.locator('text=Environmental Impact Assessment').first()).toBeVisible()

    // Lists
    await expect(page.locator('text=Carbon emissions increased by 5%')).toBeVisible()
    await expect(page.locator('text=Water usage decreased by 12%')).toBeVisible()

    // Blockquote
    await expect(page.locator('blockquote')).toBeVisible()
    await expect(page.locator('text=Our commitment to social responsibility')).toBeVisible()

    // Table
    await expect(page.locator('table')).toBeVisible()
    await expect(page.locator('text=Independent Directors')).toBeVisible()
    await expect(page.locator('text=70%')).toBeVisible()
  })

  test('should handle performance with large nested content', async ({ page }) => {
    // Generate large content with many charts and citations
    const generateLargeContent = () => {
      let text = '<h2>Large Scale Analysis</h2>'
      let citations = []

      for (let i = 1; i <= 10; i++) {
        text += `
          <h3>Section ${i}</h3>
          <p>Analysis for section ${i} shows important trends [^${700000 + i}].</p>
          <chart>
          {
            "title": { "text": "Chart ${i}" },
            "xAxis": { "type": "category", "data": ["A", "B", "C", "D"] },
            "yAxis": { "type": "value" },
            "series": [{ "data": [${i * 10}, ${i * 15}, ${i * 12}, ${i * 18}], "type": "bar" }]
          }
          </chart>
          <p>Additional analysis for section ${i} [^${700100 + i}].</p>
        `

        citations.push(
          { doc_page_id: 700000 + i, title: `Report ${i}`, url: `https://example.com/report${i}`, domain: "Environmental" },
          { doc_page_id: 700100 + i, title: `Analysis ${i}`, url: `https://example.com/analysis${i}`, domain: "Social" }
        )
      }

      return { text, citations }
    }

    const mockResponse = generateLargeContent()

    page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      })
    })

    // Measure loading time
    const startTime = Date.now()

    // Create document using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for loading
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for all content to load
    await page.waitForTimeout(5000)

    const loadTime = Date.now() - startTime

    // Verify performance is acceptable (should load within 30 seconds)
    expect(loadTime).toBeLessThan(30000)

    // Verify all charts are rendered
    const charts = page.locator('chart, [data-type="chart"]')
    await expect(charts).toHaveCount(10, { timeout: 15000 })

    // Verify all citations are rendered
    const citations = page.locator('.citation-wrapper')
    await expect(citations).toHaveCount(20, { timeout: 10000 })

    // Verify content is scrollable and interactive
    await page.locator('text=Section 10').scrollIntoViewIfNeeded()
    await expect(page.locator('text=Section 10')).toBeVisible()

    // Test that the page remains responsive
    await page.click('.ProseMirror')
    await page.keyboard.type('Performance test completed')
    await expect(page.locator('text=Performance test completed')).toBeVisible()
  })

  test('should handle concurrent loading of multiple nested components', async ({ page }) => {
    let requestCount = 0
    const requestTimes: number[] = []

    // Track concurrent requests
    page.route('**/api/report/**', route => {
      requestCount++
      requestTimes.push(Date.now())

      // Simulate different response times for different endpoints
      const delay = Math.random() * 2000 + 500 // 500-2500ms delay

      setTimeout(() => {
        route.fulfill({
          status: 200,
          headers: { 'content-type': 'application/json' },
          body: JSON.stringify({
            text: `<h3>Concurrent Section ${requestCount}</h3><p>Content loaded at ${new Date().toISOString()} [^${800000 + requestCount}].</p>`,
            citations: [
              { doc_page_id: 800000 + requestCount, title: `Concurrent Citation ${requestCount}`, url: `https://example.com/concurrent${requestCount}`, domain: "Test" }
            ]
          })
        })
      }, delay)
    })

    // Create document using testUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for entity selection
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for all components to finish loading
    await page.waitForTimeout(8000)

    // Verify multiple requests were made concurrently
    expect(requestCount).toBeGreaterThan(1)

    // Check that requests were made within a reasonable time window (indicating concurrency)
    if (requestTimes.length > 1) {
      const timeSpan = Math.max(...requestTimes) - Math.min(...requestTimes)
      expect(timeSpan).toBeLessThan(5000) // All requests should start within 5 seconds
    }

    // Verify all sections loaded successfully
    const sections = page.locator('text=Concurrent Section')
    await expect(sections.first()).toBeVisible()

    // Verify citations from concurrent loads
    const citations = page.locator('.citation-wrapper')
    await expect(citations.first()).toBeVisible()
  })
})
