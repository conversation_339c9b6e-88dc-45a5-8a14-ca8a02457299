import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';

test.describe('Navigation Sidebar', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to customer dashboard to ensure sidebar is visible
    await page.goto('/customer/dashboard');
    // Wait for the sidebar to be visible instead of networkidle
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });
  });

  test('should display sidebar with main navigation sections', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Check main navigation sections using the section testids
    await expect(page.locator('[data-testid="nav-section-dashboard"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-section-documents"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-section-analyse"]')).toBeVisible();

    // Check secondary navigation items
    await expect(page.locator('[data-testid="nav-support"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-feedback"]')).toBeVisible();

    // Check user dropdown area
    await expect(page.locator('[data-testid="user-dropdown-trigger"]')).toBeVisible();
  });

  test('should allow collapsing and expanding navigation sections', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Test collapsing dashboard section
    const dashboardSection = page.locator('[data-testid="nav-section-dashboard"]');
    const collapseButton = dashboardSection.locator('[data-testid="section-collapse-button"]');
    
    // Check initial expanded state
    await expect(dashboardSection.locator('[data-testid="section-content"]')).toBeVisible();
    
    // Collapse section
    await collapseButton.click();
    await expect(dashboardSection.locator('[data-testid="section-content"]')).not.toBeVisible();
    
    // Expand section again
    await collapseButton.click();
    await expect(dashboardSection.locator('[data-testid="section-content"]')).toBeVisible();

    // Test collapsing documents section
    const documentsSection = page.locator('[data-testid="nav-section-documents"]');
    const documentsCollapseButton = documentsSection.locator('[data-testid="section-collapse-button"]');
    
    await documentsCollapseButton.click();
    await expect(documentsSection.locator('[data-testid="section-content"]')).not.toBeVisible();
    
    await documentsCollapseButton.click();
    await expect(documentsSection.locator('[data-testid="section-content"]')).toBeVisible();
  });

  test('should persist collapsed state in localStorage', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Collapse a section
    const dashboardSection = page.locator('[data-testid="nav-section-dashboard"]');
    const collapseButton = dashboardSection.locator('[data-testid="section-collapse-button"]');
    
    await collapseButton.click();
    await expect(dashboardSection.locator('[data-testid="section-content"]')).not.toBeVisible();
    
    // Refresh page to test persistence
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Verify section remains collapsed
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
    await expect(dashboardSection.locator('[data-testid="section-content"]')).not.toBeVisible();
  });


  test('should display user dropdown menu with correct options', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Wait for user data to be loaded
    const userDropdownTrigger = page.locator('[data-testid="user-dropdown-trigger"]');
    await expect(userDropdownTrigger).toBeVisible({ timeout: 10000 });
    
    // The email is in a span with class "truncate text-xs" inside the dropdown trigger
    // Wait for the user email to be rendered in the dropdown trigger
    await expect(userDropdownTrigger.locator('span.text-xs')).toContainText('<EMAIL>', { timeout: 10000 });
    
    // Click user dropdown trigger
    await userDropdownTrigger.click();

    // Verify dropdown menu opens
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();

    // Check for expected menu items
    await expect(page.locator('[data-testid="menu-account"]')).toBeVisible();
    await expect(page.locator('[data-testid="menu-billing"]')).toBeVisible();
    await expect(page.locator('[data-testid="menu-usage"]')).toBeVisible();
    
    // Check for notifications
    await expect(page.locator('[data-testid="menu-notifications"]')).toBeVisible();

    // Check for sign out option
    await expect(page.locator('[data-testid="menu-sign-out"]')).toBeVisible();

    // Close dropdown by pressing Escape
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).not.toBeVisible();
  });

  test('should navigate to account settings from user dropdown', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Open user dropdown and click account
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    await page.click('[data-testid="menu-account"]');
    
    // Verify navigation to account page
    await page.waitForURL('**/account**');
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 15000 });
  });

  test('should navigate to billing from user dropdown', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Open user dropdown and click billing
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    await page.click('[data-testid="menu-billing"]');
    
    // Verify navigation to billing page
    await page.waitForURL('**/billing**');
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 15000 });
  });

  test('should navigate to usage from user dropdown', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Open user dropdown and click usage
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    await page.click('[data-testid="menu-usage"]');
    
    // Verify navigation to usage page
    await page.waitForURL('**/usage**');
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 15000 });
  });

  test('should display feature flag-based menu item visibility', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Check that navigation sections exist
    await expect(page.locator('[data-testid="nav-section-dashboard"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-section-documents"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-section-analyse"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-section-models"]')).toBeVisible();

    // Feature-gated items like Flags, Cherry Picking, Claims, and Promises are controlled by feature flags
    // They may or may not be visible depending on the user's features
    // The test user might not have these features enabled, so we skip checking for them
  });

  test('should navigate to notifications from user dropdown', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Wait for user data to be loaded
    const userDropdownTrigger = page.locator('[data-testid="user-dropdown-trigger"]');
    await expect(userDropdownTrigger.locator('span.text-xs')).toContainText('<EMAIL>', { timeout: 10000 });

    // Open user dropdown
    await userDropdownTrigger.click();
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    // Click notifications menu item
    await page.click('[data-testid="menu-notifications"]');
    
    // Should navigate to notifications page
    await page.waitForURL('**/notifications**', { timeout: 15000 });
    await expect(page.locator('[data-testid="notifications-page"]')).toBeVisible({ timeout: 15000 });
  });

  test('should maintain navigation state when changing routes', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Collapse a section
    const documentsSection = page.locator('[data-testid="nav-section-documents"]');
    const collapseButton = documentsSection.locator('[data-testid="section-collapse-button"]');
    
    await collapseButton.click();
    await expect(documentsSection.locator('[data-testid="section-content"]')).not.toBeVisible();

    // Navigate to different page
    await page.goto('/customer/dashboard/flags');
    await page.waitForLoadState('networkidle');

    // Verify sidebar state is maintained
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
    await expect(documentsSection.locator('[data-testid="section-content"]')).not.toBeVisible();
  });

  test('should navigate between different sections', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Navigate to dashboard
    await page.goto('/customer/dashboard');
    
    // Verify we're on dashboard
    await expect(page).toHaveURL(/\/customer\/dashboard/);

    // Click on documents section header to expand if needed
    const documentsSection = page.locator('[data-testid="nav-section-documents"]');
    const documentsSectionButton = documentsSection.locator('[data-testid="nav-documents"]').first();
    await documentsSectionButton.click();
    
    // If documents section is collapsed, it should expand
    // The actual navigation happens via the sub-items
    await expect(documentsSection.locator('[data-testid="section-content"]')).toBeVisible();
  });

  test('should handle sign out functionality', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Wait for user data to be loaded
    const userDropdownTrigger = page.locator('[data-testid="user-dropdown-trigger"]');
    await expect(userDropdownTrigger.locator('span.text-xs')).toContainText('<EMAIL>', { timeout: 10000 });

    // Open user dropdown and initiate sign out
    await userDropdownTrigger.click();
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    await page.click('[data-testid="menu-sign-out"]');
    
    // Verify redirect to home page (not login page, as per the actual implementation)
    await page.waitForURL('/', { timeout: 15000 });
  });

  test('should maintain sidebar visibility on different viewport sizes', async ({ page }) => {
    // Wait for sidebar to be visible in desktop view
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500); // Allow for responsive transition

    // Sidebar should still exist (shadcn sidebar handles responsive behavior internally)
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();

    // Return to desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(500);
    
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
  });
});