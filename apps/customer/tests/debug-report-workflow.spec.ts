import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';

test.describe('Debug Report Workflow', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    
    // Monitor console errors and logs
    page.on('console', msg => {
      console.log(`Browser console [${msg.type()}]: ${msg.text()}`);
    });
    
    // Monitor JavaScript errors
    page.on('pageerror', error => {
      console.log(`Page error: ${error.message}`);
      console.log(`Stack: ${error.stack}`);
    });
    
    // Monitor network requests
    page.on('requestfailed', request => {
      console.log(`Failed request: ${request.method()} ${request.url()} - ${request.failure()?.errorText || 'Unknown error'}`);
    });

    await testUtils.login();
  });

  test('debug document creation and editor error', async ({ page }) => {
    console.log('Starting debug test...');
    
    try {
      console.log('Creating document from template...');
      const documentId = await testUtils.createDocumentFromTemplate('ESG Report');
      console.log(`Document created with ID: ${documentId}`);
      
      // Wait longer to see what errors occur during content loading
      await page.waitForTimeout(15000);
      
      // Check if editor is in error state
      const errorMsg = page.locator('text=Something went wrong');
      if (await errorMsg.isVisible()) {
        console.log('Editor is in error state!');
        
        // Try to get more detailed error info
        const errorDetails = page.locator('group:has-text("Error Details (Development Only)")');
        if (await errorDetails.isVisible()) {
          const errorText = await errorDetails.textContent();
          console.log('Error details:', errorText);
        }
      }
      
      // Check what components exist
      const reportSections = await page.locator('.report-section').count();
      const reportGroups = await page.locator('.report-group').count();
      const reportSummaries = await page.locator('.report-summary').count();
      
      console.log(`Found ${reportSections} report sections, ${reportGroups} report groups, ${reportSummaries} report summaries`);
      
      if (reportSections === 0) {
        console.log('No report sections found - checking page content');
        const pageContent = await page.textContent('body');
        console.log('Page contains "report-section":', pageContent?.includes('report-section'));
        console.log('Page contains "ProseMirror":', pageContent?.includes('ProseMirror'));
        
        // Check if ProseMirror editor exists
        const proseMirror = page.locator('.ProseMirror');
        if (await proseMirror.isVisible()) {
          const editorContent = await proseMirror.textContent();
          console.log('Editor content:', editorContent?.slice(0, 200));
        } else {
          console.log('ProseMirror editor not found');
        }
      }
      
    } catch (error) {
      console.log('Error during test:', error);
      throw error;
    }
  });
});