/**
 * Test configuration for Playwright tests
 * 
 * This file contains configurable test data that can be adjusted based on
 * the actual data available in the test database.
 */

export interface TestConfig {
  // Authentication
  auth: {
    email: string;
    password: string;
  };
  
  // Test entities (from xfer_entities_v2)
  entities: {
    primary: string;
    secondary: string;
    invalid: string;
  };
  
  // Test runs (from xfer_runs_v2)
  runs: {
    latest: string;
    specific: number;
    invalid: string;
  };
  
  // Test models and sections (from xfer_model_sections_v2)
  models: {
    primary: string;
    secondary: string;
    invalid: string;
  };
  
  sections: {
    [model: string]: {
      primary: string;
      secondary: string;
      invalid: string;
    };
  };
  
  // Template names
  templates: {
    primary: string;
    secondary?: string;
  };
  
  // API endpoints
  endpoints: {
    baseUrl: string;
    timeout: number;
  };
}

/**
 * Default test configuration based on current database state
 * DO NOT CHANGE THIS CONFIG
 */
export const TEST_CONFIG: TestConfig = {
  auth: {
    email: '<EMAIL>',
    password: 'test1_pass',
  },
  
  entities: {
    primary: 'NxEZa0dXLn',      // Colgate
    secondary: 'JN6ZWej7Rw',    // Inflexion and Trust
    invalid: 'invalid-entity'
  },
  
  runs: {
    latest: 'latest',
    specific: 3469,             // Latest run ID with complete data (flags, claims, promises)
    invalid: 'invalid-run'
  },
  
  models: {
    primary: 'sdg',
    secondary: 'doughnut',
    invalid: 'invalid-model'
  },
  
  sections: {
    sdg: {
      primary: '13_climate_action',
      secondary: '2_zero_hunger',
      invalid: 'invalid-section'
    },
    doughnut: {
      primary: 'climate_change',
      secondary: 'biodiversity_loss',
      invalid: 'invalid-section'
    }
  },
  
  templates: {
    secondary: 'EKO Report',
    primary: 'Blank Document',
  },
  
  endpoints: {
    baseUrl: '/api',
    timeout: 30000
  }
};

/**
 * Helper function to get test data for a specific entity
 */
export function getTestEntity(type: 'primary' | 'secondary' | 'invalid' = 'primary') {
  return TEST_CONFIG.entities[type];
}

/**
 * Helper function to get test data for a specific run
 */
export function getTestRun(type: 'latest' | 'specific' | 'invalid' = 'latest') {
  return TEST_CONFIG.runs[type];
}

/**
 * Helper function to get test data for a specific model
 */
export function getTestModel(type: 'primary' | 'secondary' | 'invalid' = 'primary') {
  return TEST_CONFIG.models[type];
}

/**
 * Helper function to get test data for a specific section
 */
export function getTestSection(model: string, type: 'primary' | 'secondary' | 'invalid' = 'primary') {
  const modelSections = TEST_CONFIG.sections[model];
  if (!modelSections) {
    throw new Error(`No test sections configured for model: ${model}`);
  }
  return modelSections[type];
}

/**
 * Helper function to build API endpoint URLs
 */
export function buildApiUrl(path: string, params?: Record<string, string | number>) {
  let url = `${TEST_CONFIG.endpoints.baseUrl}${path}`;
  
  if (params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      searchParams.append(key, String(value));
    });
    url += `?${searchParams.toString()}`;
  }
  
  return url;
}

/**
 * Helper function to get authentication credentials
 */
export function getAuthCredentials() {
  return TEST_CONFIG.auth;
}

/**
 * Helper function to get template name
 */
export function getTestTemplate(type: 'primary' | 'secondary' = 'primary') {
  return type === 'primary' ? TEST_CONFIG.templates.primary : TEST_CONFIG.templates.secondary;
}
