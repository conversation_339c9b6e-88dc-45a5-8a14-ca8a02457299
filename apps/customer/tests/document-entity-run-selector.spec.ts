import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';
import { getTestEntity, getTestRun, getAuthCredentials } from './test-config';

test.describe('Document Entity Run Selector', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('should display entity and run selectors when creating a new document', async ({ page }) => {
    // Navigate to the template selection page
    await page.goto('/customer/documents/new');
    await page.waitForLoadState('networkidle');
    
    // Entity and run selectors should be visible on the template selection page
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="run-select"]')).toBeVisible();
    
    await expect(page.locator('label[for="entity-select"]')).toBeVisible();
    await expect(page.locator('label[for="run-select"]')).toBeVisible();
  });

  test('should load entities in the entity selector', async ({ page }) => {
    // Navigate to the template selection page
    await page.goto('/customer/documents/new');
    await page.waitForLoadState('networkidle');

    await page.waitForTimeout(3000);

    await page.click('[data-testid="entity-select"]');

    await page.waitForTimeout(2000);

    const optionCount = await page.locator('[role="option"]').count();

    expect(optionCount).toBeGreaterThan(0);

    await expect(page.locator('[role="option"]').first()).toBeVisible();
  });

  test('should load runs when an entity is selected', async ({ page }) => {
    // Navigate to the template selection page
    await page.goto('/customer/documents/new');
    await page.waitForLoadState('networkidle');
    
    await page.click('[data-testid="entity-select"]');
    await page.click('[role="option"]');
    
    await page.waitForTimeout(1000);
    
    await page.click('[data-testid="run-select"]');
    
    const runOptionCount = await page.locator('[role="option"]').count();
    expect(runOptionCount).toBeGreaterThan(0);
    
    await expect(page.locator('[role="option"]:has-text("Latest Run")')).toBeVisible();
  });

  test('should auto-select first entity if none is selected', async ({ page }) => {
    // Navigate to the template selection page
    await page.goto('/customer/documents/new');
    await page.waitForLoadState('networkidle');

    const entitySelector = page.locator('[data-testid="entity-select"]');

    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]');
      if (!trigger) return false;
      const text = trigger.textContent || '';
      return text !== 'Loading entities...' && text !== 'Select entity' && text.trim() !== '';
    }, { timeout: 10000 });

    const entityValue = await entitySelector.textContent();

    expect(entityValue).not.toBe('Select entity');
    expect(entityValue).not.toBe('Loading entities...');
    expect(entityValue).not.toBe('Error loading entities');
    expect(entityValue?.trim()).toBeTruthy();
  });

  test('should enable run selector when entity is selected', async ({ page }) => {
    // Navigate to the template selection page
    await page.goto('/customer/documents/new');
    await page.waitForLoadState('networkidle');
    
    await page.waitForTimeout(500);
    
    await expect(page.locator('[data-testid="run-select"]')).not.toBeDisabled();
  });

  test('should toggle disclosures switch', async ({ page }) => {
    // Navigate to the template selection page
    await page.goto('/customer/documents/new');
    await page.waitForLoadState('networkidle');
    
    const disclosuresSwitch = page.locator('#include-disclosures');
    await expect(disclosuresSwitch).toBeVisible();
    
    await expect(disclosuresSwitch).toBeChecked();
    
    await disclosuresSwitch.click();
    await expect(disclosuresSwitch).not.toBeChecked();
    
    await disclosuresSwitch.click();
    await expect(disclosuresSwitch).toBeChecked();
  });

  test('should persist entity and run selection on template page', async ({ page }) => {
    // Navigate to the template selection page
    await page.goto('/customer/documents/new');
    await page.waitForLoadState('networkidle');
    
    await page.click('[data-testid="entity-select"]');
    await page.click('[role="option"]');
    
    await page.waitForTimeout(1000);
    
    await page.click('[data-testid="run-select"]');
    await page.click('[role="option"]:has-text("Latest Run")');
    
    await page.reload();
    
    // After reload, selectors should still be visible on template page
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible();
    
    const entitySelector = page.locator('[data-testid="entity-select"]');
    const runSelector = page.locator('[data-testid="run-select"]');
    
    await expect(entitySelector).not.toBeDisabled();
    await expect(runSelector).not.toBeDisabled();
  });

  test('should handle loading states gracefully', async ({ page }) => {
    // Navigate to the template selection page
    await page.goto('/customer/documents/new');
    await page.waitForLoadState('networkidle');

    const entitySelector = page.locator('[data-testid="entity-select"]');

    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]');
      if (!trigger) return false;
      const text = trigger.textContent || '';
      return text !== 'Loading entities...' && text.trim() !== '';
    }, { timeout: 10000 });

    const finalText = await entitySelector.textContent();
    expect(finalText).not.toBe('Loading entities...');
  });

  test('should NOT display entity and run selectors on existing document editor', async ({ page }) => {
    await page.goto('/customer/documents');
    
    const existingDoc = page.locator('[data-testid="document-item"]').first();
    
    await existingDoc.click();
    
    // Entity and run selectors should NOT be visible on the editor page
    await expect(page.locator('[data-testid="entity-select"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="run-select"]')).not.toBeVisible();
    
    await expect(page.locator('label[for="entity-select"]')).not.toBeVisible();
    await expect(page.locator('label[for="run-select"]')).not.toBeVisible();
  });

  test('should handle errors gracefully', async ({ page }) => {
    await page.route('**/view_my_companies*', route => 
      route.fulfill({ 
        status: 500, 
        body: JSON.stringify({ error: 'Database error' }) 
      })
    );
    
    // Navigate to the template selection page
    await page.goto('/customer/documents/new');
    await page.waitForLoadState('networkidle');
    
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="run-select"]')).toBeVisible();
    
    await expect(page.locator('[data-testid="entity-select"]')).toBeDisabled();
  });
});