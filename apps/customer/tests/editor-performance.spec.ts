import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Performance Tests', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Enable detailed console logging for performance tracking
    page.on('console', msg => {
      if (msg.text().includes('performance') || msg.text().includes('timing')) {
        console.log(`Performance log: ${msg.text()}`)
      }
    })

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test.describe('Rendering Performance', () => {
    test('should render large documents efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Mark start time
      const startTime = Date.now()

      // Insert many components to test performance
      const componentCount = 20
      
      for (let i = 0; i < componentCount; i++) {
        await page.click('button[title="Insert Report Section"]')
        
        // Add some variability - insert groups every 5th component
        if (i % 5 === 0) {
          await page.click('button[title="Insert Report Group"]')
        }
        
        // Small delay to prevent overwhelming the system
        await page.waitForTimeout(50)
      }

      // Wait for all components to be registered and loaded
      await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(componentCount)
      
      // Wait for all components to reach loaded state
      const components = page.locator('[data-component-type="report-section"]')
      const count = await components.count()
      
      for (let i = 0; i < count; i++) {
        await expect(components.nth(i)).toHaveAttribute('data-status', 'loaded', { timeout: 30000 })
      }

      const endTime = Date.now()
      const totalTime = endTime - startTime

      // Should complete within reasonable time (30 seconds for 20 components)
      expect(totalTime).toBeLessThan(30000)
      
      // Log performance metrics
      console.log(`Rendered ${componentCount} components in ${totalTime}ms (${totalTime/componentCount}ms per component)`)
    })

    test('should handle rapid component updates efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Insert a group with several components
      await page.click('button[title="Insert Report Group"]')
      const group = page.locator('[data-component-type="report-group"]').first()
      await expect(group).toBeVisible()

      // Add components to the group
      await group.click()
      for (let i = 0; i < 10; i++) {
        await page.click('button[title="Insert Report Section"]')
      }

      await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(10)

      // Measure performance of rapid status updates
      const startTime = await page.evaluate(() => performance.now())

      // Simulate rapid component status changes by refreshing all components
      const components = page.locator('[data-component-type="report-section"]')
      const count = await components.count()

      for (let i = 0; i < count; i++) {
        // Trigger refresh on each component
        await components.nth(i).click()
        await page.keyboard.press('r') // Assuming 'r' is a refresh hotkey
        await page.waitForTimeout(10) // Very small delay
      }

      // Wait for all updates to complete
      await page.waitForTimeout(2000)

      const endTime = await page.evaluate(() => performance.now())
      const updateTime = endTime - startTime

      // Should handle rapid updates efficiently (under 5 seconds)
      expect(updateTime).toBeLessThan(5000)
      
      console.log(`Processed ${count} rapid updates in ${updateTime}ms`)
    })

    test('should debounce group status updates effectively', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Track group status update frequency
      let updateCount = 0
      const updateTimes: number[] = []

      await page.evaluate(() => {
        // Override console.log to track group status updates
        const originalLog = console.log
        console.log = (...args: any[]) => {
          const message = args.join(' ')
          if (message.includes('Group status update') || message.includes('GroupStatusManager')) {
            (window as any).groupUpdates = (window as any).groupUpdates || []
            ;(window as any).groupUpdates.push(Date.now())
          }
          originalLog.apply(console, args)
        }
      })

      // Create a complex nested structure
      await page.click('button[title="Insert Report Group"]')
      const parentGroup = page.locator('[data-component-type="report-group"]').first()
      await expect(parentGroup).toBeVisible()

      // Rapidly add many child components
      await parentGroup.click()
      const startTime = Date.now()

      for (let i = 0; i < 15; i++) {
        await page.click('button[title="Insert Report Section"]')
        // No delay - test rapid-fire updates
      }

      // Wait for debouncing to settle
      await page.waitForTimeout(1000)

      const updates = await page.evaluate(() => (window as any).groupUpdates || [])
      const totalOperations = 15

      // Should have significantly fewer updates than operations due to debouncing
      expect(updates.length).toBeLessThan(totalOperations)
      expect(updates.length).toBeGreaterThan(0)

      console.log(`${totalOperations} operations resulted in ${updates.length} group updates (${((1 - updates.length/totalOperations) * 100).toFixed(1)}% reduction)`)
    })
  })

  test.describe('Memory Performance', () => {
    test('should maintain stable memory usage during component lifecycle', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Get initial memory baseline
      const initialMemory = await page.evaluate(() => {
        return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0
      })

      // Perform multiple cycles of adding and removing components
      for (let cycle = 0; cycle < 5; cycle++) {
        // Add components
        for (let i = 0; i < 5; i++) {
          await page.click('button[title="Insert Report Section"]')
        }

        await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(5)

        // Remove all components
        await page.keyboard.press('Control+a')
        await page.keyboard.press('Delete')
        
        await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(0)
        
        // Wait for cleanup
        await page.waitForTimeout(500)
      }

      // Final memory check
      const finalMemory = await page.evaluate(() => {
        return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0
      })

      if (initialMemory > 0 && finalMemory > 0) {
        const memoryGrowth = (finalMemory - initialMemory) / initialMemory
        
        // Memory should not grow significantly (allowing for some variance)
        expect(memoryGrowth).toBeLessThan(0.3) // Less than 30% growth
        
        console.log(`Memory usage: ${initialMemory} -> ${finalMemory} (${(memoryGrowth * 100).toFixed(1)}% change)`)
      }
    })

    test('should cleanup resources properly when navigating away', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Create components with various timers and listeners
      await page.click('button[title="Insert Report Group"]')
      await page.click('button[title="Insert Report Section"]')
      await page.click('button[title="Insert Report Section"]')

      await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(2)

      // Check resource tracking
      const resourcesBefore = await page.evaluate(() => {
        return (window as any).editorResources ? Object.keys((window as any).editorResources).length : 0
      })

      // Navigate away
      await page.goto('/customer/dashboard')
      await page.waitForLoadState('networkidle')

      // Wait for cleanup to occur
      await page.waitForTimeout(1000)

      // Navigate back to check cleanup
      await page.goto(`/customer/document/${documentId}`)
      await testUtils.waitForEditor()

      const resourcesAfter = await page.evaluate(() => {
        return (window as any).editorResources ? Object.keys((window as any).editorResources).length : 0
      })

      // Resources should have been cleaned up (or reset for new session)
      console.log(`Resources before navigation: ${resourcesBefore}, after: ${resourcesAfter}`)
    })
  })

  test.describe('Network Performance', () => {
    test('should batch API calls efficiently', async ({ page }) => {
      let apiCallCount = 0
      const apiCalls: string[] = []

      // Track API calls
      page.route('**/api/**', (route) => {
        apiCallCount++
        apiCalls.push(route.request().url())
        route.continue()
      })

      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add multiple components rapidly
      const startTime = Date.now()
      
      for (let i = 0; i < 10; i++) {
        await page.click('button[title="Insert Report Section"]')
      }

      // Wait for all components to load
      await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(10)
      
      // Wait for all to reach loaded state
      const components = page.locator('[data-component-type="report-section"]')
      for (let i = 0; i < 10; i++) {
        await expect(components.nth(i)).toHaveAttribute('data-status', 'loaded', { timeout: 30000 })
      }

      const endTime = Date.now()

      // Should make reasonable number of API calls (not one per component if batched)
      console.log(`Made ${apiCallCount} API calls for 10 components in ${endTime - startTime}ms`)
      
      // Log API call patterns
      const uniqueEndpoints = [...new Set(apiCalls.map(url => {
        const parts = url.split('/')
        return parts[parts.length - 2] || 'unknown'
      }))]
      
      console.log(`Unique API endpoints called: ${uniqueEndpoints.join(', ')}`)
    })

    test('should handle slow network conditions gracefully', async ({ page }) => {
      // Simulate slow network
      await page.route('**/api/report/entity/**', async (route) => {
        // Add delay to simulate slow network
        await new Promise(resolve => setTimeout(resolve, 1000))
        route.continue()
      })

      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      const startTime = Date.now()

      // Insert components under slow network conditions
      await page.click('button[title="Insert Report Section"]')
      await page.click('button[title="Insert Report Section"]')

      // Should show appropriate loading states
      const components = page.locator('[data-component-type="report-section"]')
      await expect(components).toHaveCount(2)

      // Components should be in loading state initially
      await expect(components.first()).toHaveAttribute('data-status', 'loading')

      // Eventually should load despite slow network
      await expect(components.first()).toHaveAttribute('data-status', 'loaded', { timeout: 30000 })
      await expect(components.nth(1)).toHaveAttribute('data-status', 'loaded', { timeout: 30000 })

      const endTime = Date.now()
      console.log(`Loaded 2 components under slow network in ${endTime - startTime}ms`)
    })
  })

  test.describe('Interaction Performance', () => {
    test('should respond to user interactions quickly', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Create a document with several components
      for (let i = 0; i < 5; i++) {
        await page.click('button[title="Insert Report Section"]')
      }

      await expect(page.locator('[data-component-type="report-section"]')).toHaveCount(5)

      // Test response time to clicking on components
      const components = page.locator('[data-component-type="report-section"]')
      
      for (let i = 0; i < 5; i++) {
        const startTime = await page.evaluate(() => performance.now())
        
        await components.nth(i).click()
        
        // Wait for selection/focus to be applied
        await expect(components.nth(i)).toHaveClass(/selected|focused/)
        
        const endTime = await page.evaluate(() => performance.now())
        const responseTime = endTime - startTime

        // Should respond within reasonable time (under 100ms)
        expect(responseTime).toBeLessThan(100)
      }
    })

    test('should handle rapid keyboard input efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Focus the editor
      await page.locator('.ProseMirror').click()

      const startTime = await page.evaluate(() => performance.now())

      // Type rapidly
      const testText = 'This is a test of rapid typing performance in the refactored editor context system'
      await page.keyboard.type(testText, { delay: 10 }) // 10ms between keystrokes

      const endTime = await page.evaluate(() => performance.now())
      const typingTime = endTime - startTime

      // Should handle typing efficiently
      const expectedTime = testText.length * 10 * 1.5 // Allow 50% overhead
      expect(typingTime).toBeLessThan(expectedTime)

      // Verify all text was captured
      await expect(page.locator('.ProseMirror')).toContainText(testText)

      console.log(`Typed ${testText.length} characters in ${typingTime}ms (${(typingTime/testText.length).toFixed(1)}ms per character)`)
    })
  })
})