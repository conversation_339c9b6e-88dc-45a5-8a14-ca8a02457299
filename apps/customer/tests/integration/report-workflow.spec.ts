import { test, expect } from '@playwright/test';
import { TestUtils } from '../helpers/tests/test-utils';

test.describe('Report Workflow Integration', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('complete report creation and editing workflow', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate('ESG Report');
    
    // Wait for components to be created and visible in the DOM first
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 30000 });
    await expect(page.locator('.report-group').first()).toBeVisible({ timeout: 30000 });
    await expect(page.locator('.report-summary').first()).toBeVisible({ timeout: 30000 });
    
    expect(await testUtils.countComponents('report-summary')).toBeGreaterThan(0);
    expect(await testUtils.countComponents('report-group')).toBeGreaterThan(0);
    expect(await testUtils.countComponents('report-section')).toBeGreaterThan(0);
    
    await testUtils.waitForComponentLoading('.report-section');
    
    await testUtils.typeInEditor('This is a custom introduction to the ESG report.');
    
    await testUtils.addReportComponent('section', {
      id: 'custom-section',
      title: 'Custom Analysis',
      prompt: 'Provide analysis on custom metrics'
    });
    
    await testUtils.checkComponentExists('custom-section');
    
    await testUtils.openComponentConfig('.report-section');
    await testUtils.fillComponentConfig({
      prompt: 'Updated prompt for better analysis'
    });
    await testUtils.confirmComponentConfig();
    
    await testUtils.performComponentAction('lock', '.report-section');
    await testUtils.checkComponentState('locked', '.report-section');
    
    await testUtils.performComponentAction('refresh', '[data-id="custom-section"]');
    await testUtils.waitForComponentLoading('[data-id="custom-section"]');
    
    await testUtils.goToDocuments();
    await page.goto(`/customer/documents/${documentId}`);
    await testUtils.checkEditorContent('This is a custom introduction to the ESG report.');
    await testUtils.checkComponentExists('custom-section');
    
    await testUtils.checkForErrors();
  });

  test('report summary dependency workflow', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('ESG Report');
    
    await testUtils.waitForComponentLoading('.report-section');
    
    await testUtils.addReportComponent('summary', {
      id: 'custom-summary',
      title: 'Custom Summary',
      prompt: 'Summarize the key findings'
    });
    
    await testUtils.openComponentConfig('.report-summary[data-id="custom-summary"]');
    
    await page.click('text=Select components to summarize');
    await page.click('text=environmental-risks');
    await page.click('text=social-risks');
    
    await testUtils.confirmComponentConfig();
    
    await testUtils.waitForComponentLoading('.report-summary[data-id="custom-summary"]');
    
    await testUtils.checkComponentExists('custom-summary');
  });

  test('error handling and recovery', async ({ page }) => {
    await testUtils.mockApiError('/api/report/entity');

    await testUtils.createDocumentFromTemplate('ESG Report');
    
    await expect(page.locator('.error, [role="alert"]')).toBeVisible({ timeout: 10000 });
    
    await page.unroute('**/api/report/entity**');
    
    await testUtils.performComponentAction('refresh', '.report-section');
    
    await testUtils.waitForComponentLoading('.report-section');
  });

  test('collaborative editing simulation', async ({ page, context }) => {
    const documentId = await testUtils.createDocumentFromTemplate('ESG Report');
    
    const page2 = await context.newPage();
    const testUtils2 = new TestUtils(page2);
    await testUtils2.login('<EMAIL>', 'demo');
    await page2.goto(`/customer/documents/${documentId}`);
    
    await testUtils.typeInEditor('User 1 content');
    await testUtils.waitForAutoSave();
    
    await testUtils2.checkEditorContent('User 1 content');
    
    await testUtils2.typeInEditor(' and User 2 content');
    await testUtils2.waitForAutoSave();
    
    await testUtils.checkEditorContent('User 1 content and User 2 content');
    
    await page2.close();
  });

  test('performance with large documents', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('ESG Report');
    
    for (let i = 0; i < 5; i++) {
      await testUtils.addReportComponent('section', {
        id: `perf-section-${i}`,
        title: `Performance Section ${i}`,
        prompt: `Analysis for section ${i}`
      });
    }
    
    for (let i = 0; i < 5; i++) {
      await testUtils.checkComponentExists(`perf-section-${i}`);
    }
    
    await testUtils.typeInEditor('Performance test content');
    await expect(page.locator('text=Performance test content')).toBeVisible();
    
    const startTime = Date.now();
    await testUtils.waitForComponentLoading('.report-section');
    const loadTime = Date.now() - startTime;
    
    expect(loadTime).toBeLessThan(30000);
  });

  test('accessibility compliance', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('ESG Report');
    
    const buttonCount = await page.locator('[role="button"]').count();
    expect(buttonCount).toBeGreaterThan(0);
    await expect(page.locator('[role="dialog"]')).toHaveCount(0);
    
    await testUtils.openComponentConfig('.report-section');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('[aria-label="Close"]')).toBeVisible();
    
    await page.keyboard.press('Tab');
    await page.keyboard.press('Escape');
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test('mobile responsiveness', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await testUtils.createDocumentFromTemplate('ESG Report');
    
    await expect(page.locator('.report-section')).toBeVisible();
    await expect(page.locator('.report-group')).toBeVisible();
    
    const reportSection = page.locator('.report-section').first();
    const menuTrigger = reportSection.locator('button[role="button"]').last();
    await menuTrigger.click();
    await expect(page.locator('text=Configure')).toBeVisible();
  });

  test('data persistence across sessions', async ({ page, context }) => {
    const documentId = await testUtils.createDocumentFromTemplate('ESG Report');
    await testUtils.typeInEditor('Persistence test content');
    await testUtils.waitForAutoSave();
    
    await page.close();
    const newPage = await context.newPage();
    const newTestUtils = new TestUtils(newPage);
    await newTestUtils.login();
    
    await newPage.goto(`/customer/documents/${documentId}`);
    
    await newTestUtils.checkEditorContent('Persistence test content');
  });
});