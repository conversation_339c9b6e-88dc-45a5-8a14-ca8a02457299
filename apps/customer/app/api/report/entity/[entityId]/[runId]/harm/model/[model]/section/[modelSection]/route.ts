import { NextRequest } from 'next/server'
import { FlagTypeV2, ModelSectionType } from '@/types'
import { truncate } from '@/utils/text-utils'
import { COMMON_INSTRUCTIONS, PROMPT_PRESERVE_CITATIONS } from '@/app/api/report/report-common'
import { callValidatedLLMsWithTools, LLMModel, Message, LLMOptions } from '@/app/api/report/validated-llms'
import { chartTools } from '@/app/api/report/charts-tools'
import {
  fetchEntityData,
  fetchFlagsData,
  fetchModelSectionsData,
  fetchRunData,
} from '@/app/api/report/data-fetchers'

export const maxDuration = 180;

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ entityId: string; runId: string; model: string; modelSection: string }> }
) {
  try {
    // Get the parameters from the route
    const params = await context.params;
    const { entityId, runId, model, modelSection } = params;

    const { searchParams } = new URL(request.url)
    const includeDisclosures = searchParams.get('includeDisclosures') === 'true'

    if (!entityId || !runId || !model || !modelSection) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: entityId, runId, model, modelSection' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    console.log(`[API] /api/report/entity/${entityId}/${runId}/harm/model/${model}/section/${modelSection} called with:`, {
      entityId,
      runId,
      model,
      modelSection,
      includeDisclosures
    })

    // Fetch entity data
    const entityData = await fetchEntityData(entityId)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(runId, entityId)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch all flags for this entity and run
    const allFlags = await fetchFlagsData({
      entity: entityId,
      runId: runData.id.toString(),
      model,
      includeDisclosures
    })

    // Filter flags for this specific section
    const flags = allFlags.filter((flag: FlagTypeV2) =>
      flag.model.model_sections[model] === modelSection
    ).sort((a: FlagTypeV2, b: FlagTypeV2) => -(a.model?.impact || 0) + (b.model?.impact || 0)).slice(0, 40)

    // Fetch model sections to get section name
    const modelSections = await fetchModelSectionsData(model)
    const modelSectionData = modelSections.find((ms: ModelSectionType) => ms.section === modelSection)
    const sectionName = modelSectionData?.title || modelSection.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())

    const entityName = entityData.name
    const modelName = model

    console.log(`[API] Processing section:`, {
      sectionName,
      entityName,
      modelName,
      runData,
      flagsCount: flags.length
    })

    if (flags.length === 0) {
      // Return notApplicable response if no flags found
      return new Response(JSON.stringify({
        text: `STATUS:NOT_APPLICABLE`,
        citations: [],
        notApplicable: true,
        metadata: {
          entityId,
          modelSection,
          sectionName,
          entityName,
          modelName,
          flagsCount: 0,
          citationCount: 0,
          generatedAt: new Date().toISOString(),
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Build the prompt for this section
    const flagSummaries = flags.map((flag: FlagTypeV2) => {
      const summary = truncate(flag.model?.flag_summary || '', 500)
      const analysis = truncate(flag.model?.flag_analysis || '', 300)
      return `**${flag.model?.flag_title}** (Impact: ${flag.model?.impact || 'N/A'})\n${summary}\n${analysis}`
    }).join('\n\n')

    const promptText = `
      <instructions>
      You are analyzing the ${sectionName} impacts for ${entityName} based on the following evidence.
      
      Please provide a comprehensive analysis that:
      1. Summarizes the key ${sectionName.toLowerCase()} impacts and issues
      2. Identifies patterns and trends in the evidence
      3. Highlights the most significant positive and negative impacts
      4. Provides context about the severity and scope of impacts
      5. Maintains all citation references in the format [^citation_id]
      6. Use charts to visualize data when appropriate. You have access to chart creation tools that can create:
         - Area charts for trends over time
         - Bar charts for comparing values across categories
         - Line charts for showing changes over time
         - Pie charts for showing proportions
         - Radar charts for comparing multiple metrics
         - Radial charts for progress indicators

      When you identify data that would benefit from visualization, use the appropriate chart tool to create it.
      
      Structure your response with clear headings and organize the information logically.
      Focus on factual analysis based on the evidence provided.
      
      Evidence for ${sectionName}:
      ${flagSummaries}
      
      ${COMMON_INSTRUCTIONS}
      ${PROMPT_PRESERVE_CITATIONS}
      </instructions>
    `;

    // Extract citations from the flags used in this section first (for validation)
    const validCitations = flags
      .filter((flag: FlagTypeV2) => flag.model?.citations && flag.model.citations.length > 0)
      .flatMap((flag: FlagTypeV2) => flag.model.citations)
      .filter((citation: any, index: number, self: any[]) =>
        index === self.findIndex((c: any) => c.doc_page_id === citation.doc_page_id)
      ); // Remove duplicates

    // Create validation function for charts and citations
    const createValidator = (validCitationIds: string[]) => {
      return (response: string | null): boolean | string => {
        if (!response) {
          return 'Response is null or empty'
        }

        // 1. Validate chart JSON if present
        const chartMatches = response.match(/<chart[^>]*>([\s\S]*?)<\/chart>/g)
        if (chartMatches) {
          for (const chartMatch of chartMatches) {
            // Check if this is a base64-encoded chart (from chart tools)
            const base64Match = chartMatch.match(/data-json="([^"]+)"/)
            if (base64Match) {
              // Validate base64-encoded chart data
              try {
                const base64Data = base64Match[1]
                const jsonString = Buffer.from(base64Data, 'base64').toString('utf-8')
                JSON.parse(jsonString)
              } catch (error) {
                return `Chart contains invalid base64-encoded JSON: ${error instanceof Error ? error.message : 'Unknown parsing error'}`
              }
            } else {
              // Legacy format: plain JSON inside chart tags
              const jsonContent = chartMatch.replace(/<\/?chart[^>]*>/g, '').trim()
              if (jsonContent) {
                try {
                  JSON.parse(jsonContent)
                } catch (error) {
                  return `Chart contains invalid JSON: ${error instanceof Error ? error.message : 'Unknown parsing error'}`
                }
              }
            }
          }
        }

        // 2. Validate citations if present
        const citationMatches = response.match(/\[\^(\d+)]/g)
        if (citationMatches) {
          for (const citationMatch of citationMatches) {
            const citationId = citationMatch.replace(/\[\^(\d+)]/, '$1')
            if (!validCitationIds.includes(citationId)) {
              return `Citation [^${citationId}] is not valid. Valid citations are: ${validCitationIds.join(', ')}`
            }
          }
        }

        return true
      }
    }

    // Get citation IDs for validation
    const validCitationIds = validCitations.map((citation: any) => citation.doc_page_id?.toString() || '')
      .filter(id => id !== '')

    // Generate content using tool-enabled LLM with chart and citation validation
    const messages: Message[] = [{ role: 'user', content: promptText }];
    
    const llmOptions: LLMOptions = {
      cacheKey: `entity-${entityId}-run-${runId}-model-${model}-section-${modelSection}`,
      cachePrefix: 'entity-section-tools',
      maxOutputTokens: 16000,
      escalateTo: [LLMModel.GEMINI_PRO],
      appendOnEvalFail: true,
      eval: createValidator(validCitationIds),
      evalRetryMessage: 'Please provide a more comprehensive analysis, use charts where appropriate to visualize the data, and ensure all citations are valid.'
    };

    const text = await callValidatedLLMsWithTools(
      [LLMModel.GEMINI_FLASH],
      messages,
      chartTools,
      llmOptions,
      5 // max tool iterations
    );

    if (!text) {
      throw new Error('Failed to generate entity section content');
    }

    const citations = validCitations

    // Return JSON response with text and citations
    const response = {
      text,
      citations,
      metadata: {
        entityId,
        modelSection,
        sectionName,
        entityName,
        modelName,
        runId: runData.id,
        flagsCount: flags.length,
        citationCount: citations.length,
        includeDisclosures,
        generatedAt: new Date().toISOString(),
      }
    };

    return new Response(JSON.stringify(response), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400'
      }
    });

  } catch (error) {
    console.error(`[API] /api/report/entity/harm/model/section error:`, error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate section report', 
        message: error instanceof Error ? error.message : String(error) 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
