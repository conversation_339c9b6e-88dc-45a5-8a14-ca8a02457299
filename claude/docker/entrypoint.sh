#!/bin/bash
identifier=$1
shift
export PATH="/root/.local/bin:$PATH"
# Entrypoint script for Claude Docker container
set -e
# Copy mounted Claude configuration files to writable locations if they exist and if there is no /home/<USER>/.claude.json already
if [ ! -f "/home/<USER>/.claude.json" ]; then
    if [ -f "/workspace/.claude.json" ]; then
        echo "Copying Claude configuration..."
        cp "/workspace/.claude.json" "/home/<USER>/.claude.json"
    fi
fi

# Copy mounted Claude directory files to writable locations if they exist and if there is no /home/<USER>/.claude already
if [ ! -d "/home/<USER>/.claude" ]; then
    if [ -d "/workspace/.claude" ]; then
        echo "Copying Claude directory..."
        cp -r "/workspace/.claude" "/home/<USER>"
    fi
fi

cp -f /workspace/.claude/.credentials.json /home/<USER>/.claude/.credentials.json

# Ensure the workspace directory is accessible
cd /workspace/$identifier

cat /home/<USER>/.claude.json > /dev/null

pnpm config set store-dir /home/<USER>/.pnpm-store
pnpm store path

yes | pnpm install --frozen-lockfile --prefer-offline --store-dir  /home/<USER>/.pnpm-store
cd apps/customer && npx playwright install chromium && cd -
#cd backoffice/src
##uv run python cli.py --help
#cd -
echo 'alias tsc="npx tsc"' >> ~/.bashrc
git config user.email "<EMAIL>" && git config user.name "Claude as Neil"

# If no arguments provided, start Claude interactively
if [ $# -eq 0 ]; then
    echo "Starting Claude Code..."
    claude
else
    echo "Executing Claude Code with arguments: $@"
    # Pass all arguments to claude
    claude "$@"
fi
